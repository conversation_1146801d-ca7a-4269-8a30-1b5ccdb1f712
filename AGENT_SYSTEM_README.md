# DeepSite AI Agent System

## Overview

The DeepSite AI Agent System is a comprehensive multi-agent architecture built with LangGraph that enhances the existing DeepSite platform by creating specialized AI agents that collaborate to build complete, production-ready websites.

## Architecture

### Agent Types

1. **Supervisor Agent** - Orchestrates the entire workflow and coordinates between agents
2. **Content Generation Agent** - Creates website copy, headlines, and text content
3. **Design Agent** - Makes visual design decisions, color schemes, and layouts
4. **Code Generation Agent** - Generates HTML, CSS, and JavaScript code
5. **File Management Agent** - Organizes project structure and files
6. **Quality Assurance Agent** - Reviews and validates the final website

### Workflow Process

```
User Requirements → Supervisor Agent → Content Generation → Design → Code Generation → File Management → Quality Assurance → Final Website
```

## Features

### Multi-Agent Collaboration
- **Specialized Expertise**: Each agent focuses on specific aspects of website creation
- **Intelligent Handoffs**: Agents automatically pass control to the next appropriate agent
- **State Management**: Shared state ensures consistency across all agents
- **Error Handling**: Robust error handling with recovery mechanisms

### User Interaction
- **Progress Tracking**: Real-time progress updates with visual indicators
- **Approval Gates**: User approval required at key milestones
- **Interactive Interface**: Modern React-based UI for agent management
- **File Preview**: Live preview of generated files and website structure

### Quality Assurance
- **Code Validation**: Automatic HTML, CSS, and JavaScript validation
- **Accessibility Checks**: WCAG 2.1 compliance verification
- **Performance Analysis**: Loading speed and optimization recommendations
- **SEO Optimization**: Meta tags, structured data, and SEO best practices

## Implementation Details

### Backend Architecture

#### Agent System (`src/agents/`)
- **Types** (`types.ts`): TypeScript interfaces and state definitions
- **Tools** (`tools/index.ts`): Shared tools for file operations, content generation, etc.
- **Individual Agents**: Specialized agent implementations
- **Workflow** (`workflow.ts`): LangGraph orchestration and state management

#### API Endpoints (`server.js`)
- `POST /api/agents/start-workflow` - Initialize agent workflow
- `GET /api/agents/stream/:sessionId` - Stream workflow progress
- `GET /api/agents/status/:sessionId` - Get workflow status
- `POST /api/agents/approve/:sessionId` - Approve workflow steps
- `GET /api/agents/config` - Get agent configurations

### Frontend Architecture

#### Components (`src/components/agent-workflow/`)
- **AgentWorkflow** (`agent-workflow.tsx`): Main agent interface component
- **UI Components**: Progress bars, status indicators, approval dialogs

#### Integration
- **Header Updates**: New "AI Agents" tab in navigation
- **App Integration**: Seamless integration with existing DeepSite features
- **State Management**: React state management for agent workflow

## Usage

### Starting an Agent Workflow

1. Navigate to the "AI Agents" tab in DeepSite
2. Fill out the website requirements form:
   - Website description
   - Type (landing page, portfolio, blog, etc.)
   - Style (modern, classic, minimal, etc.)
   - Target audience
3. Click "Start AI Agent Workflow"

### Monitoring Progress

- **Real-time Updates**: Progress bar shows completion status
- **Agent Status**: Visual indicators show which agent is currently active
- **File Generation**: Live view of generated files
- **Error Reporting**: Clear error messages and recovery options

### Approval Process

- **Design Approval**: Review and approve design decisions
- **Final Approval**: Review complete website before delivery
- **Feedback Loop**: Provide feedback for improvements

## Configuration

### Environment Variables

```bash
# AI Provider API Keys
OPENAI_API_KEY=your_openai_key
ANTHROPIC_API_KEY=your_anthropic_key
GOOGLE_API_KEY=your_google_key

# Existing DeepSite variables
OAUTH_CLIENT_ID=your_oauth_client_id
OAUTH_CLIENT_SECRET=your_oauth_client_secret
```

### Agent Configuration

Each agent can be configured with:
- **Model Selection**: Choose between different AI models
- **Provider Selection**: OpenAI, Anthropic, Google, etc.
- **Custom Prompts**: Customize agent behavior
- **Tool Selection**: Enable/disable specific tools

## Development

### Adding New Agents

1. Create agent class in `src/agents/`
2. Implement required methods (`execute`, `getConfig`)
3. Add agent to workflow in `workflow.ts`
4. Update UI components as needed

### Adding New Tools

1. Define tool in `src/agents/tools/index.ts`
2. Use LangChain tool format with Zod schema
3. Add to relevant agent configurations
4. Test tool functionality

### Extending Functionality

- **Custom Workflows**: Create specialized workflows for different website types
- **Integration Points**: Add hooks for external services
- **Monitoring**: Add detailed logging and analytics
- **Caching**: Implement caching for improved performance

## Benefits

### For Users
- **Faster Development**: Complete websites generated in minutes
- **Professional Quality**: AI-generated content and code follows best practices
- **Customization**: Tailored to specific requirements and preferences
- **Learning**: Understand website structure through generated code

### For Developers
- **Modular Architecture**: Easy to extend and maintain
- **Type Safety**: Full TypeScript support
- **Testing**: Comprehensive testing framework
- **Documentation**: Well-documented codebase

## Future Enhancements

### Planned Features
- **Multi-language Support**: Generate websites in multiple languages
- **Advanced Integrations**: CMS, e-commerce, analytics integration
- **Template Library**: Pre-built templates and components
- **Collaboration**: Multi-user workflows and team features

### Technical Improvements
- **Performance Optimization**: Faster agent execution
- **Advanced AI Models**: Integration with latest AI models
- **Cloud Deployment**: Scalable cloud infrastructure
- **Real-time Collaboration**: Live editing and collaboration features

## Troubleshooting

### Common Issues

1. **Agent Workflow Fails to Start**
   - Check API key configuration
   - Verify network connectivity
   - Review error logs

2. **Slow Performance**
   - Check AI provider rate limits
   - Optimize agent prompts
   - Review system resources

3. **Quality Issues**
   - Adjust QA criteria
   - Provide more detailed requirements
   - Review agent configurations

### Support

For technical support and questions:
- Check the main DeepSite documentation
- Review agent system logs
- Contact the development team

## Contributing

We welcome contributions to the DeepSite AI Agent System:

1. Fork the repository
2. Create a feature branch
3. Implement your changes
4. Add tests and documentation
5. Submit a pull request

## License

This project is licensed under the MIT License - see the main DeepSite LICENSE file for details.
