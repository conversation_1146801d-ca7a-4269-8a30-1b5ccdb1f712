import express from "express";
import path from "path";
import { fileURLToPath } from "url";
import dotenv from "dotenv";
import cookieParser from "cookie-parser";
import {
  createRepo,
  uploadFiles,
  whoAmI,
  spaceInfo,
  fileExists,
} from "@huggingface/hub";
import { InferenceClient } from "@huggingface/inference";
import { GoogleGenerativeAI } from "@google/generative-ai";
import OpenAI from "openai";
import bodyParser from "body-parser";

import checkUser from "./middlewares/checkUser.js";
import { MODELS, PROVIDERS } from "./utils/providers.js";
import { COLORS } from "./utils/colors.js";

// Import agent workflow (will be created)
// import { AgentWorkflow } from "./src/agents/workflow.js";

// Load environment variables from .env file
dotenv.config();

const app = express();

const ipAddresses = new Map();

// Initialize agent workflow (will be uncommented when agents are ready)
// const agentWorkflow = new AgentWorkflow();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const PORT = process.env.APP_PORT || 3000;
const REDIRECT_URI =
  process.env.REDIRECT_URI || `http://localhost:${PORT}/auth/login`;
const MAX_REQUESTS_PER_IP = 2;

const SEARCH_START = "<<<<<<< SEARCH";
const DIVIDER = "=======";
const REPLACE_END = ">>>>>>> REPLACE";

app.use(cookieParser());
app.use(bodyParser.json());
app.use(express.static(path.join(__dirname, "dist")));

// Initialize Google AI client
let googleAI = null;
if (process.env.GOOGLE_API_KEY) {
  googleAI = new GoogleGenerativeAI(process.env.GOOGLE_API_KEY);
}

// Helper function for Google API calls
async function callGoogleAPI(model, messages, maxTokens = 1000000) {
  if (!googleAI) {
    throw new Error("Google API key not configured");
  }

  const genModel = googleAI.getGenerativeModel({ model });

  // Convert messages to Google format
  const systemMessage = messages.find(m => m.role === 'system');
  const userMessages = messages.filter(m => m.role === 'user' || m.role === 'assistant');

  let prompt = '';
  if (systemMessage) {
    prompt += `${systemMessage.content}\n\n`;
  }

  // Combine user and assistant messages into a conversation
  userMessages.forEach(msg => {
    if (msg.role === 'user') {
      prompt += `User: ${msg.content}\n`;
    } else if (msg.role === 'assistant') {
      prompt += `Assistant: ${msg.content}\n`;
    }
  });

  const result = await genModel.generateContent(prompt);
  const response = await result.response;
  return response.text();
}

// Helper function for Google API streaming calls
async function* callGoogleAPIStream(model, messages, maxTokens = 1000000) {
  if (!googleAI) {
    throw new Error("Google API key not configured");
  }

  const genModel = googleAI.getGenerativeModel({ model });

  // Convert messages to Google format
  const systemMessage = messages.find(m => m.role === 'system');
  const userMessages = messages.filter(m => m.role === 'user' || m.role === 'assistant');

  let prompt = '';
  if (systemMessage) {
    prompt += `${systemMessage.content}\n\n`;
  }

  // Combine user and assistant messages into a conversation
  userMessages.forEach(msg => {
    if (msg.role === 'user') {
      prompt += `User: ${msg.content}\n`;
    } else if (msg.role === 'assistant') {
      prompt += `Assistant: ${msg.content}\n`;
    }
  });

  const result = await genModel.generateContentStream(prompt);

  for await (const chunk of result.stream) {
    const chunkText = chunk.text();
    if (chunkText) {
      yield {
        choices: [{
          delta: {
            content: chunkText
          }
        }]
      };
    }
  }
}

// Helper function for OpenRouter API calls
async function callOpenRouterAPI(model, messages, maxTokens = 4000, apiKey, customModel) {
  if (!apiKey) {
    throw new Error("OpenRouter API key not configured");
  }

  const openai = new OpenAI({
    baseURL: "https://openrouter.ai/api/v1",
    apiKey: apiKey,
  });

  // Use custom model if the selected model is "custom" and customModel is provided
  const modelToUse = model === "custom" && customModel && customModel.trim() ? customModel : model;

  // Adjust max_tokens based on common model limits
  let adjustedMaxTokens = maxTokens;
  if (modelToUse.includes('gpt-4o-mini')) {
    adjustedMaxTokens = Math.min(maxTokens, 16000);
  } else if (modelToUse.includes('gpt-4o')) {
    adjustedMaxTokens = Math.min(maxTokens, 4000);
  } else if (modelToUse.includes('claude')) {
    adjustedMaxTokens = Math.min(maxTokens, 4000);
  } else if (modelToUse.includes('llama')) {
    adjustedMaxTokens = Math.min(maxTokens, 4000);
  }

  try {
    const response = await openai.chat.completions.create({
      model: modelToUse,
      messages: messages,
      max_tokens: adjustedMaxTokens,
      temperature: 0.7,
    });

    // Add proper error handling for response validation
    if (!response || !response.choices || !Array.isArray(response.choices) || response.choices.length === 0) {
      throw new Error("Invalid response from OpenRouter API");
    }

    return response.choices[0]?.message?.content;
  } catch (error) {
    console.error("OpenRouter API error:", error);
    throw new Error(`OpenRouter API call failed: ${error.message}`);
  }
}

// Helper function for OpenRouter API streaming calls
async function* callOpenRouterAPIStream(model, messages, maxTokens = 4000, apiKey, customModel) {
  if (!apiKey) {
    throw new Error("OpenRouter API key not configured");
  }

  const openai = new OpenAI({
    baseURL: "https://openrouter.ai/api/v1",
    apiKey: apiKey,
  });

  // Use custom model if the selected model is "custom" and customModel is provided
  const modelToUse = model === "custom" && customModel && customModel.trim() ? customModel : model;

  // Adjust max_tokens based on common model limits
  let adjustedMaxTokens = maxTokens;
  if (modelToUse.includes('gpt-4o-mini')) {
    adjustedMaxTokens = Math.min(maxTokens, 16000);
  } else if (modelToUse.includes('gpt-4o')) {
    adjustedMaxTokens = Math.min(maxTokens, 4000);
  } else if (modelToUse.includes('claude')) {
    adjustedMaxTokens = Math.min(maxTokens, 4000);
  } else if (modelToUse.includes('llama')) {
    adjustedMaxTokens = Math.min(maxTokens, 4000);
  }



  const stream = await openai.chat.completions.create({
    model: modelToUse,
    messages: messages,
    max_tokens: adjustedMaxTokens,
    temperature: 0.7,
    stream: true,
  });

  for await (const chunk of stream) {
    const content = chunk.choices[0]?.delta?.content;
    if (content) {
      yield {
        choices: [{
          delta: {
            content: content
          }
        }]
      };
    }
  }
}



// Agent Workflow API Endpoints

// Start agent workflow
app.post("/api/agents/start-workflow", async (req, res) => {
  try {
    const { requirements, sessionId } = req.body;

    if (!requirements || !sessionId) {
      return res.status(400).send({
        ok: false,
        message: "Missing requirements or sessionId",
      });
    }

    // Create workflow context
    const context = {
      sessionId,
      userId: req.cookies.hf_token ? "authenticated" : "anonymous",
      requirements,
      currentStep: 0,
      totalSteps: 5,
      startTime: new Date(),
      apiKeys: {
        openai: process.env.OPENAI_API_KEY,
        anthropic: process.env.ANTHROPIC_API_KEY,
        google: process.env.GOOGLE_API_KEY,
        openrouter: req.body.openrouterApiKey,
      },
    };

    // For now, return a placeholder response
    // When agents are ready, uncomment:
    // const result = await agentWorkflow.executeWorkflow(requirements, context);

    res.status(200).send({
      ok: true,
      sessionId,
      message: "Agent workflow started (placeholder)",
      // result,
    });
  } catch (error) {
    console.error("Agent workflow error:", error);
    res.status(500).send({
      ok: false,
      message: error.message || "Failed to start agent workflow",
    });
  }
});

// Stream agent workflow progress
app.get("/api/agents/stream/:sessionId", async (req, res) => {
  try {
    const { sessionId } = req.params;

    // Set up SSE headers
    res.setHeader("Content-Type", "text/event-stream");
    res.setHeader("Cache-Control", "no-cache");
    res.setHeader("Connection", "keep-alive");
    res.setHeader("Access-Control-Allow-Origin", "*");

    // For now, send placeholder events
    // When agents are ready, uncomment:
    // const stream = agentWorkflow.streamWorkflow(requirements, context);

    res.write(`data: ${JSON.stringify({
      type: "status",
      message: "Agent workflow streaming (placeholder)",
      timestamp: new Date().toISOString(),
    })}\n\n`);

    // Keep connection alive
    const keepAlive = setInterval(() => {
      res.write(`data: ${JSON.stringify({
        type: "heartbeat",
        timestamp: new Date().toISOString(),
      })}\n\n`);
    }, 30000);

    req.on("close", () => {
      clearInterval(keepAlive);
    });

  } catch (error) {
    console.error("Agent stream error:", error);
    res.status(500).send({
      ok: false,
      message: error.message || "Failed to stream agent workflow",
    });
  }
});

// Get workflow status
app.get("/api/agents/status/:sessionId", async (req, res) => {
  try {
    const { sessionId } = req.params;

    // For now, return placeholder status
    // When agents are ready, uncomment:
    // const state = await agentWorkflow.getWorkflowState(sessionId);

    res.status(200).send({
      ok: true,
      sessionId,
      status: "placeholder",
      // state,
    });
  } catch (error) {
    console.error("Get workflow status error:", error);
    res.status(500).send({
      ok: false,
      message: error.message || "Failed to get workflow status",
    });
  }
});

// Approve workflow step
app.post("/api/agents/approve/:sessionId", async (req, res) => {
  try {
    const { sessionId } = req.params;
    const { approved, feedback } = req.body;

    // For now, return placeholder response
    // When agents are ready, uncomment:
    // const success = await agentWorkflow.approveWorkflow(sessionId, approved);

    res.status(200).send({
      ok: true,
      sessionId,
      approved,
      message: "Workflow approval processed (placeholder)",
    });
  } catch (error) {
    console.error("Workflow approval error:", error);
    res.status(500).send({
      ok: false,
      message: error.message || "Failed to process workflow approval",
    });
  }
});

// Get agent configurations
app.get("/api/agents/config", (_req, res) => {
  try {
    // For now, return placeholder configs
    // When agents are ready, uncomment:
    // const configs = agentWorkflow.getAgentConfigs();

    const placeholderConfigs = {
      supervisor: { name: "Supervisor Agent", type: "supervisor" },
      contentGeneration: { name: "Content Generation Agent", type: "content_generation" },
      design: { name: "Design Agent", type: "design" },
      codeGeneration: { name: "Code Generation Agent", type: "code_generation" },
      fileManagement: { name: "File Management Agent", type: "file_management" },
      qualityAssurance: { name: "Quality Assurance Agent", type: "quality_assurance" },
    };

    res.status(200).send({
      ok: true,
      configs: placeholderConfigs,
    });
  } catch (error) {
    console.error("Get agent configs error:", error);
    res.status(500).send({
      ok: false,
      message: error.message || "Failed to get agent configurations",
    });
  }
});

app.get("/api/login", (_req, res) => {
  const redirectUrl = `https://huggingface.co/oauth/authorize?client_id=${process.env.OAUTH_CLIENT_ID}&redirect_uri=${REDIRECT_URI}&response_type=code&scope=openid%20profile%20write-repos%20manage-repos%20inference-api&prompt=consent&state=1234567890`;
  res.status(200).send({
    ok: true,
    redirectUrl,
  });
});
app.get("/auth/login", async (req, res) => {
  const { code } = req.query;

  if (!code) {
    return res.redirect(302, "/");
  }
  const Authorization = `Basic ${Buffer.from(
    `${process.env.OAUTH_CLIENT_ID}:${process.env.OAUTH_CLIENT_SECRET}`
  ).toString("base64")}`;

  const request_auth = await fetch("https://huggingface.co/oauth/token", {
    method: "POST",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
      Authorization,
    },
    body: new URLSearchParams({
      grant_type: "authorization_code",
      code: code,
      redirect_uri: REDIRECT_URI,
    }),
  });

  const response = await request_auth.json();

  if (!response.access_token) {
    return res.redirect(302, "/");
  }

  res.cookie("hf_token", response.access_token, {
    httpOnly: false,
    secure: true,
    sameSite: "none",
    maxAge: 30 * 24 * 60 * 60 * 1000,
  });

  return res.redirect(302, "/");
});
app.get("/auth/logout", (req, res) => {
  res.clearCookie("hf_token", {
    httpOnly: false,
    secure: true,
    sameSite: "none",
  });
  return res.redirect(302, "/");
});

app.get("/api/@me", checkUser, async (req, res) => {
  let { hf_token } = req.cookies;

  if (process.env.HF_TOKEN && process.env.HF_TOKEN !== "") {
    return res.send({
      preferred_username: "local-use",
      isLocalUse: true,
    });
  }

  try {
    const request_user = await fetch("https://huggingface.co/oauth/userinfo", {
      headers: {
        Authorization: `Bearer ${hf_token}`,
      },
    });

    const user = await request_user.json();
    res.send(user);
  } catch (err) {
    res.clearCookie("hf_token", {
      httpOnly: false,
      secure: true,
      sameSite: "none",
    });
    res.status(401).send({
      ok: false,
      message: err.message,
    });
  }
});

app.post("/api/deploy", checkUser, async (req, res) => {
  const { html, title, path, prompts } = req.body;
  if (!html || (!path && !title)) {
    return res.status(400).send({
      ok: false,
      message: "Missing required fields",
    });
  }

  let { hf_token } = req.cookies;
  if (process.env.HF_TOKEN && process.env.HF_TOKEN !== "") {
    hf_token = process.env.HF_TOKEN;
  }

  try {
    const repo = {
      type: "space",
      name: path ?? "",
    };

    let readme;
    let newHtml = html;

    if (!path || path === "") {
      const { name: username } = await whoAmI({ accessToken: hf_token });
      const newTitle = title
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, "-")
        .split("-")
        .filter(Boolean)
        .join("-")
        .slice(0, 96);

      const repoId = `${username}/${newTitle}`;
      repo.name = repoId;

      await createRepo({
        repo,
        accessToken: hf_token,
      });
      const colorFrom = COLORS[Math.floor(Math.random() * COLORS.length)];
      const colorTo = COLORS[Math.floor(Math.random() * COLORS.length)];
      readme = `---
title: ${newTitle}
emoji: 🐳
colorFrom: ${colorFrom}
colorTo: ${colorTo}
sdk: static
pinned: false
tags:
  - deepsite
---

Check out the configuration reference at https://huggingface.co/docs/hub/spaces-config-reference`;
    }

    newHtml = html.replace(/<\/body>/, `</body>`);
    const file = new Blob([newHtml], { type: "text/html" });
    file.name = "index.html"; // Add name property to the Blob

    // create prompt.txt file with all the prompts used, split by new line
    const newPrompts = ``.concat(prompts.map((prompt) => prompt).join("\n"));
    const promptFile = new Blob([newPrompts], { type: "text/plain" });
    promptFile.name = "prompts.txt"; // Add name property to the Blob

    const files = [file, promptFile];
    if (readme) {
      const readmeFile = new Blob([readme], { type: "text/markdown" });
      readmeFile.name = "README.md"; // Add name property to the Blob
      files.push(readmeFile);
    }
    await uploadFiles({
      repo,
      files,
      commitTitle: `${prompts[prompts.length - 1]} - ${
        prompts.length > 1 ? "Follow Up" : "Initial"
      } Deployment`,
      accessToken: hf_token,
    });
    return res.status(200).send({ ok: true, path: repo.name });
  } catch (err) {
    return res.status(500).send({
      ok: false,
      message: err.message,
    });
  }
});

app.post("/api/ask-ai", async (req, res) => {
  const { prompt, provider, model, redesignMarkdown, openrouterApiKey, customModel } = req.body;
  if (!model) {
    return res.status(400).send({
      ok: false,
      message: "Missing required fields",
    });
  }
  if (!redesignMarkdown && !prompt) {
    return res.status(400).send({
      ok: false,
      message: "Missing required fields",
    });
  }

  const initialSystemPrompt = `ONLY USE HTML, CSS AND JAVASCRIPT. If you want to use ICON make sure to import the library first. Try to create the best UI possible by using only HTML, CSS and JAVASCRIPT. MAKE IT RESPONSIVE USING TAILWINDCSS. Use as much as you can TailwindCSS for the CSS, if you can't do something with TailwindCSS, then use custom CSS (make sure to import <script src="https://cdn.tailwindcss.com"></script> in the head). Also, try to ellaborate as much as you can, to create something unique. ALWAYS GIVE THE RESPONSE INTO A SINGLE HTML FILE`;

  const selectedModel = MODELS.find(
    (m) => m.value === model || m.label === model
  );
  if (!selectedModel) {
    return res.status(400).send({
      ok: false,
      message: "Invalid model selected",
    });
  }
  if (!selectedModel.providers.includes(provider) && provider !== "auto") {
    return res.status(400).send({
      ok: false,
      openSelectProvider: true,
      message: `The selected model does not support the ${provider} provider.`,
    });
  }

  let { hf_token } = req.cookies;
  let token = hf_token;
  let billTo = null;

  if (process.env.HF_TOKEN && process.env.HF_TOKEN !== "") {
    token = process.env.HF_TOKEN;
  }

  const ip =
    req.headers["x-forwarded-for"]?.split(",")[0].trim() ||
    req.headers["x-real-ip"] ||
    req.socket.remoteAddress ||
    req.ip ||
    "0.0.0.0";

  if (!token) {
    ipAddresses.set(ip, (ipAddresses.get(ip) || 0) + 1);
    if (ipAddresses.get(ip) > MAX_REQUESTS_PER_IP) {
      return res.status(429).send({
        ok: false,
        openLogin: true,
        message: "Log In to continue using the service",
      });
    }

    token = process.env.DEFAULT_HF_TOKEN;
    billTo = "huggingface";
  }

  // Set up response headers for streaming
  res.setHeader("Content-Type", "text/plain");
  res.setHeader("Cache-Control", "no-cache");
  res.setHeader("Connection", "keep-alive");

  const client = new InferenceClient(token);
  let completeResponse = "";

  let TOKENS_USED = prompt?.length;

  const DEFAULT_PROVIDER = PROVIDERS.novita;
  const selectedProvider =
    provider === "auto"
      ? PROVIDERS[selectedModel.autoProvider]
      : PROVIDERS[provider] ?? DEFAULT_PROVIDER;

  if (provider !== "auto" && TOKENS_USED >= selectedProvider.max_tokens) {
    return res.status(400).send({
      ok: false,
      openSelectProvider: true,
      message: `Context is too long. ${selectedProvider.name} allow ${selectedProvider.max_tokens} max tokens.`,
    });
  }
  try {
    // Handle Google provider separately
    if (selectedProvider.id === "google") {
      if (!process.env.GOOGLE_API_KEY) {
        return res.status(400).send({
          ok: false,
          openSelectProvider: true,
          message: "Google API key not configured. Please select a different provider.",
        });
      }

      const messages = [
        {
          role: "system",
          content: initialSystemPrompt,
        },
        {
          role: "user",
          content: redesignMarkdown
            ? `Here is my current design as a markdown:\n\n${redesignMarkdown}\n\nNow, please create a new design based on this markdown.`
            : prompt,
        },
      ];

      const googleStream = callGoogleAPIStream(selectedModel.value, messages, selectedProvider.max_tokens);

      for await (const chunk of googleStream) {
        const chunkText = chunk.choices[0]?.delta?.content;
        if (chunkText) {
          res.write(chunkText);
          completeResponse += chunkText;

          if (completeResponse.includes("</html>")) {
            break;
          }
        }
      }
    } else if (selectedProvider.id === "openrouter") {
      // Handle OpenRouter provider separately
      if (!openrouterApiKey) {
        return res.status(400).send({
          ok: false,
          openSelectProvider: true,
          message: "OpenRouter API key not configured. Please enter your API key in settings.",
        });
      }

      const messages = [
        {
          role: "system",
          content: initialSystemPrompt,
        },
        {
          role: "user",
          content: redesignMarkdown
            ? `Here is my current design as a markdown:\n\n${redesignMarkdown}\n\nNow, please create a new design based on this markdown.`
            : prompt,
        },
      ];

      const openrouterStream = callOpenRouterAPIStream(selectedModel.value, messages, selectedProvider.max_tokens, openrouterApiKey, customModel);

      for await (const chunk of openrouterStream) {
        const chunkText = chunk.choices[0]?.delta?.content;
        if (chunkText) {
          res.write(chunkText);
          completeResponse += chunkText;

          if (completeResponse.includes("</html>")) {
            break;
          }
        }
      }
    } else {
      // Use Hugging Face inference for other providers
      const chatCompletion = client.chatCompletionStream(
        {
          model: selectedModel.value,
          provider: selectedProvider.id,
          messages: [
            {
              role: "system",
              content: initialSystemPrompt,
            },
            {
              role: "user",
              content: redesignMarkdown
                ? `Here is my current design as a markdown:\n\n${redesignMarkdown}\n\nNow, please create a new design based on this markdown.`
                : prompt,
            },
          ],
          max_tokens: selectedProvider.max_tokens,
        },
        billTo ? { billTo } : {}
      );

      while (true) {
        const { done, value } = await chatCompletion.next();
        if (done) {
          break;
        }
        const chunk = value.choices[0]?.delta?.content;
        if (chunk) {
          let newChunk = chunk;
          if (!selectedModel?.isThinker) {
            if (provider !== "sambanova") {
              res.write(chunk);
              completeResponse += chunk;

              if (completeResponse.includes("</html>")) {
                break;
              }
            } else {
              let newChunk = chunk;
              if (chunk.includes("</html>")) {
                newChunk = newChunk.replace(/<\/html>[\s\S]*/, "</html>");
              }
              completeResponse += newChunk;
              res.write(newChunk);
              if (newChunk.includes("</html>")) {
                break;
              }
            }
          } else {
            const lastThinkTagIndex = completeResponse.lastIndexOf("</think>");
            completeResponse += newChunk;
            res.write(newChunk);
            if (lastThinkTagIndex !== -1) {
              const afterLastThinkTag = completeResponse.slice(
                lastThinkTagIndex + "</think>".length
              );
              if (afterLastThinkTag.includes("</html>")) {
                break;
              }
            }
          }
        }
      }
    }
    // End the response stream
    res.end();
  } catch (error) {
    if (error.message.includes("exceeded your monthly included credits")) {
      return res.status(402).send({
        ok: false,
        openProModal: true,
        message: error.message,
      });
    }
    if (!res.headersSent) {
      res.status(500).send({
        ok: false,
        message:
          error.message || "An error occurred while processing your request.",
      });
    } else {
      // Otherwise end the stream
      res.end();
    }
  }
});

app.put("/api/ask-ai", async (req, res) => {
  const { prompt, html, previousPrompt, openrouterApiKey, customModel, provider, model } = req.body;
  if (!prompt || !html) {
    return res.status(400).send({
      ok: false,
      message: "Missing required fields",
    });
  }
  const followUpSystemPrompt = `You are an expert web developer modifying an existing HTML file.
The user wants to apply changes based on their request.
You MUST output ONLY the changes required using the following SEARCH/REPLACE block format. Do NOT output the entire file.
Explain the changes briefly *before* the blocks if necessary, but the code changes THEMSELVES MUST be within the blocks.
Format Rules:
1. Start with ${SEARCH_START}
2. Provide the exact lines from the current code that need to be replaced.
3. Use ${DIVIDER} to separate the search block from the replacement.
4. Provide the new lines that should replace the original lines.
5. End with ${REPLACE_END}
6. You can use multiple SEARCH/REPLACE blocks if changes are needed in different parts of the file.
7. To insert code, use an empty SEARCH block (only ${SEARCH_START} and ${DIVIDER} on their lines) if inserting at the very beginning, otherwise provide the line *before* the insertion point in the SEARCH block and include that line plus the new lines in the REPLACE block.
8. To delete code, provide the lines to delete in the SEARCH block and leave the REPLACE block empty (only ${DIVIDER} and ${REPLACE_END} on their lines).
9. IMPORTANT: The SEARCH block must *exactly* match the current code, including indentation and whitespace.
Example Modifying Code:
\`\`\`
Some explanation...
${SEARCH_START}
    <h1>Old Title</h1>
${DIVIDER}
    <h1>New Title</h1>
${REPLACE_END}
${SEARCH_START}
  </body>
${DIVIDER}
    <script>console.log("Added script");</script>
  </body>
${REPLACE_END}
\`\`\`
Example Deleting Code:
\`\`\`
Removing the paragraph...
${SEARCH_START}
  <p>This paragraph will be deleted.</p>
${DIVIDER}
${REPLACE_END}
\`\`\``;

  // Get the selected model and provider
  const selectedModel = MODELS.find(
    (m) => m.value === model || m.label === model
  );
  if (!selectedModel) {
    return res.status(400).send({
      ok: false,
      message: "Invalid model selected",
    });
  }
  if (!selectedModel.providers.includes(provider) && provider !== "auto") {
    return res.status(400).send({
      ok: false,
      message: `The selected model does not support the ${provider} provider.`,
    });
  }

  let { hf_token } = req.cookies;
  let token = hf_token;
  let billTo = null;

  if (process.env.HF_TOKEN && process.env.HF_TOKEN !== "") {
    token = process.env.HF_TOKEN;
  }

  const ip =
    req.headers["x-forwarded-for"]?.split(",")[0].trim() ||
    req.headers["x-real-ip"] ||
    req.socket.remoteAddress ||
    req.ip ||
    "0.0.0.0";

  if (!token) {
    ipAddresses.set(ip, (ipAddresses.get(ip) || 0) + 1);
    if (ipAddresses.get(ip) > MAX_REQUESTS_PER_IP) {
      return res.status(429).send({
        ok: false,
        openLogin: true,
        message: "Log In to continue using the service",
      });
    }

    token = process.env.DEFAULT_HF_TOKEN;
    billTo = "huggingface";
  }

  const client = new InferenceClient(token);

  const DEFAULT_PROVIDER = PROVIDERS.novita;
  const selectedProvider =
    provider === "auto"
      ? PROVIDERS[selectedModel.autoProvider]
      : PROVIDERS[provider] ?? DEFAULT_PROVIDER;

  let chunk;

  try {
    // Handle Google provider separately for follow-up requests
    if (selectedProvider.id === "google") {
      if (!process.env.GOOGLE_API_KEY) {
        return res.status(400).send({
          ok: false,
          message: "Google API key not configured. Please select a different provider.",
        });
      }

      const messages = [
        {
          role: "system",
          content: followUpSystemPrompt,
        },
        {
          role: "user",
          content: previousPrompt
            ? previousPrompt
            : "You are modifying the HTML file based on the user's request.",
        },
        {
          role: "assistant",
          content: `The current code is: \n\`\`\`html\n${html}\n\`\`\``,
        },
        {
          role: "user",
          content: prompt,
        },
      ];

      chunk = await callGoogleAPI(selectedModel.value, messages, selectedProvider.max_tokens);
    } else if (selectedProvider.id === "openrouter") {
      // Handle OpenRouter provider for follow-up requests
      if (!openrouterApiKey) {
        return res.status(400).send({
          ok: false,
          message: "OpenRouter API key not configured. Please enter your API key in settings.",
        });
      }

      const messages = [
        {
          role: "system",
          content: followUpSystemPrompt,
        },
        {
          role: "user",
          content: previousPrompt
            ? previousPrompt
            : "You are modifying the HTML file based on the user's request.",
        },
        {
          role: "assistant",
          content: `The current code is: \n\`\`\`html\n${html}\n\`\`\``,
        },
        {
          role: "user",
          content: prompt,
        },
      ];

      chunk = await callOpenRouterAPI(selectedModel.value, messages, selectedProvider.max_tokens, openrouterApiKey, customModel);
    } else {
      // Use Hugging Face inference for other providers
      const response = await client.chatCompletion(
        {
          model: selectedModel.value,
          provider: selectedProvider.id,
          messages: [
            {
              role: "system",
              content: followUpSystemPrompt,
            },
            {
              role: "user",
              content: previousPrompt
                ? previousPrompt
                : "You are modifying the HTML file based on the user's request.",
            },
            {
              role: "assistant",
              content: `The current code is: \n\`\`\`html\n${html}\n\`\`\``,
            },
            {
              role: "user",
              content: prompt,
            },
          ],
          ...(selectedProvider.id !== "sambanova"
            ? {
                max_tokens: selectedProvider.max_tokens,
              }
            : {}),
        },
        billTo ? { billTo } : {}
      );

      chunk = response.choices[0]?.message?.content;
    }
    // TO DO: handle the case where there are multiple SEARCH/REPLACE blocks
    if (!chunk) {
      return res.status(400).send({
        ok: false,
        message: "No content returned from the model",
      });
    }

    if (chunk) {
      let newHtml = html;
      // array of arrays to hold updated lines (start and end line numbers)
      const updatedLines = [];

      // Find all search/replace blocks in the chunk
      let position = 0;
      let moreBlocks = true;

      while (moreBlocks) {
        const searchStartIndex = chunk.indexOf(SEARCH_START, position);
        if (searchStartIndex === -1) {
          moreBlocks = false;
          continue;
        }

        const dividerIndex = chunk.indexOf(DIVIDER, searchStartIndex);
        if (dividerIndex === -1) {
          moreBlocks = false;
          continue;
        }

        const replaceEndIndex = chunk.indexOf(REPLACE_END, dividerIndex);
        if (replaceEndIndex === -1) {
          moreBlocks = false;
          continue;
        }

        // Extract the search and replace blocks
        const searchBlock = chunk.substring(
          searchStartIndex + SEARCH_START.length,
          dividerIndex
        );
        const replaceBlock = chunk.substring(
          dividerIndex + DIVIDER.length,
          replaceEndIndex
        );

        // Apply the replacement
        if (searchBlock.trim() === "") {
          // Inserting at the beginning
          newHtml = `${replaceBlock}\n${newHtml}`;

          // Track first line as updated
          updatedLines.push([1, replaceBlock.split("\n").length]);
        } else {
          // Find the position of the search block in the HTML
          const blockPosition = newHtml.indexOf(searchBlock);
          if (blockPosition !== -1) {
            // Count lines before the search block
            const beforeText = newHtml.substring(0, blockPosition);
            const startLineNumber = beforeText.split("\n").length;

            // Count lines in search and replace blocks
            const replaceLines = replaceBlock.split("\n").length;

            // Calculate end line (start + length of replaced content)
            const endLineNumber = startLineNumber + replaceLines - 1;

            // Track the line numbers that were updated
            updatedLines.push([startLineNumber, endLineNumber]);

            // Perform the replacement
            newHtml = newHtml.replace(searchBlock, replaceBlock);
          }
        }

        // Move position to after this block to find the next one
        position = replaceEndIndex + REPLACE_END.length;
      }

      return res.status(200).send({
        ok: true,
        html: newHtml,
        updatedLines,
      });
    } else {
      return res.status(400).send({
        ok: false,
        message: "No content returned from the model",
      });
    }
  } catch (error) {
    if (error.message.includes("exceeded your monthly included credits")) {
      return res.status(402).send({
        ok: false,
        openProModal: true,
        message: error.message,
      });
    }
    if (!res.headersSent) {
      res.status(500).send({
        ok: false,
        message:
          error.message || "An error occurred while processing your request.",
      });
    }
  }
});

app.get("/api/remix/:username/:repo", async (req, res) => {
  const { username, repo } = req.params;
  const { hf_token } = req.cookies;

  let token = hf_token || process.env.DEFAULT_HF_TOKEN;

  if (process.env.HF_TOKEN && process.env.HF_TOKEN !== "") {
    token = process.env.HF_TOKEN;
  }

  const repoId = `${username}/${repo}`;

  const url = `https://huggingface.co/spaces/${repoId}/raw/main/index.html`;
  try {
    const space = await spaceInfo({
      name: repoId,
      accessToken: token,
      additionalFields: ["author"],
    });

    if (!space || space.sdk !== "static" || space.private) {
      return res.status(404).send({
        ok: false,
        message: "Space not found",
      });
    }

    const response = await fetch(url);
    if (!response.ok) {
      return res.status(404).send({
        ok: false,
        message: "Space not found",
      });
    }
    let html = await response.text();
   
    let user = null;

    if (token) {
      const request_user = await fetch(
        "https://huggingface.co/oauth/userinfo",
        {
          headers: {
            Authorization: `Bearer ${hf_token}`,
          },
        }
      )
        .then((res) => res.json())
        .catch(() => null);

      user = request_user;
    }

    res.status(200).send({
      ok: true,
      html,
      isOwner: space.author === user?.preferred_username,
      path: repoId,
    });
  } catch (error) {
    return res.status(500).send({
      ok: false,
      message: error.message,
    });
  }
});

app.post("/api/enhance-prompt", async (req, res) => {
  const { prompt } = req.body;
  if (!prompt || typeof prompt !== "string" || !prompt.trim()) {
    return res.status(400).send({
      ok: false,
      message: "Missing or invalid prompt",
    });
  }

  // Use Gemini 2.5 Flash for prompt enhancement by default
  const enhanceModel = "gemini-2.5-flash-preview-05-20";

  // Check if Google API key is configured
  if (!process.env.GOOGLE_API_KEY) {
    return res.status(400).send({
      ok: false,
      message: "Google API key not configured for prompt enhancement.",
    });
  }

  const systemPrompt = `You are a prompt enhancement assistant. Your job is to improve user prompts to make them more specific, clear, and effective for AI code generation.

Guidelines for enhancement:
1. Make prompts more specific and detailed
2. Add context about the desired outcome
3. Include relevant technical details when appropriate
4. Maintain the original intent while improving clarity
5. Keep the enhanced prompt concise but comprehensive
6. Focus on actionable instructions

Return only the enhanced prompt without any additional explanation or formatting.`;

  const userPrompt = `Please enhance this prompt for better AI code generation results:

"${prompt.trim()}"`;

  const messages = [
    { role: "system", content: systemPrompt },
    { role: "user", content: userPrompt }
  ];

  let enhancedPrompt;

  try {
    // Use Gemini 2.5 Flash for prompt enhancement
    enhancedPrompt = await callGoogleAPI(enhanceModel, messages, 500);

    if (!enhancedPrompt || !enhancedPrompt.trim()) {
      return res.status(500).send({
        ok: false,
        message: "Failed to generate enhanced prompt",
      });
    }

    res.status(200).send({
      ok: true,
      enhancedPrompt: enhancedPrompt.trim(),
    });

  } catch (error) {
    console.error("Prompt enhancement error:", error);

    // Handle specific error cases
    if (error.message && error.message.includes("exceeded your monthly included credits")) {
      return res.status(402).send({
        ok: false,
        message: error.message,
      });
    }

    res.status(500).send({
      ok: false,
      message: error.message || "Failed to enhance prompt. Please try again.",
    });
  }
});

app.post("/api/re-design", async (req, res) => {
  const { url } = req.body;
  if (!url) {
    return res.status(400).send({
      ok: false,
      message: "Missing required fields",
    });
  }

  // call the api https://r.jina.ai/{url} and return the response
  try {
    const response = await fetch(
      `https://r.jina.ai/${encodeURIComponent(url)}`,
      {
        method: "POST",
      }
    );
    if (!response.ok) {
      return res.status(500).send({
        ok: false,
        message: "Failed to fetch redesign",
      });
    }
    // return the html response
    const markdown = await response.text();
    return res.status(200).send({
      ok: true,
      markdown,
    });
  } catch (error) {
    return res.status(500).send({
      ok: false,
      message: error.message,
    });
  }
});
app.get("*", (_req, res) => {
  res.sendFile(path.join(__dirname, "dist", "index.html"));
});

app.listen(PORT, () => {
  console.log(`Server is running on port ${PORT}`);
});
