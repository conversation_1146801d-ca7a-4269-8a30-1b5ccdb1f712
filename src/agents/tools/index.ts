import { tool } from "@langchain/core/tools";
import { z } from "zod";
import { ProjectFile } from "../types";
import { Command } from "@langchain/langgraph";
import FileManager from "../../utils/file-manager.js";

// File system tools
export const createFileTool = tool(
  async (input: { path: string; content: string; type: string; description?: string }) => {
    try {
      // For HTML files, create them in the file manager
      if (input.type === "html") {
        const fileName = input.path.endsWith('.html') ? input.path : `${input.path}.html`;
        const savedFile = FileManager.createFile(fileName, input.content);

        return new Command({
          update: {
            files: [{
              path: savedFile.name,
              content: savedFile.html,
              type: "html" as any,
              description: input.description,
            }],
            messages: [{
              role: "assistant",
              content: `Created HTML file: ${savedFile.name}`,
            }],
          },
        });
      } else {
        // For other file types, just track them in the workflow state
        const newFile: ProjectFile = {
          path: input.path,
          content: input.content,
          type: input.type as any,
          description: input.description,
        };

        return new Command({
          update: {
            files: [newFile],
            messages: [{
              role: "assistant",
              content: `Created file: ${input.path}`,
            }],
          },
        });
      }
    } catch (error) {
      return new Command({
        update: {
          messages: [{
            role: "assistant",
            content: `Error creating file: ${error instanceof Error ? error.message : 'Unknown error'}`,
          }],
        },
      });
    }
  },
  {
    name: "create_file",
    description: "Create a new file in the project",
    schema: z.object({
      path: z.string().describe("File path relative to project root"),
      content: z.string().describe("File content"),
      type: z.enum(["html", "css", "js", "json", "md", "txt"]).describe("File type"),
      description: z.string().optional().describe("Optional file description"),
    }),
  }
);

export const updateFileContentTool = tool(
  async (input: { path: string; content: string }) => {
    try {
      // For HTML files, update them in the file manager
      if (input.path.endsWith('.html')) {
        const files = FileManager.getFiles();
        const file = files.find(f => f.name === input.path);

        if (file) {
          FileManager.updateFile(file.id, input.content, "AI Agent Update");

          return new Command({
            update: {
              messages: [{
                role: "assistant",
                content: `Updated HTML file: ${input.path}`,
              }],
            },
          });
        } else {
          // File doesn't exist, create it
          const savedFile = FileManager.createFile(input.path, input.content);

          return new Command({
            update: {
              files: [{
                path: savedFile.name,
                content: savedFile.html,
                type: "html" as any,
              }],
              messages: [{
                role: "assistant",
                content: `Created new HTML file: ${savedFile.name}`,
              }],
            },
          });
        }
      } else {
        // For other file types, just track the update
        return new Command({
          update: {
            messages: [{
              role: "assistant",
              content: `Updated file: ${input.path}`,
            }],
          },
        });
      }
    } catch (error) {
      return new Command({
        update: {
          messages: [{
            role: "assistant",
            content: `Error updating file: ${error instanceof Error ? error.message : 'Unknown error'}`,
          }],
        },
      });
    }
  },
  {
    name: "update_file_content",
    description: "Update the content of an existing file",
    schema: z.object({
      path: z.string().describe("File path to update"),
      content: z.string().describe("New file content"),
    }),
  }
);

export const readFileTool = tool(
  async (input: { path: string }) => {
    try {
      // For HTML files, read from file manager
      if (input.path.endsWith('.html')) {
        const files = FileManager.getFiles();
        const file = files.find(f => f.name === input.path);

        if (file) {
          return file.html;
        } else {
          return `File not found: ${input.path}`;
        }
      } else {
        // For other file types, return placeholder
        return `Content of ${input.path} (placeholder)`;
      }
    } catch (error) {
      return `Error reading file: ${error instanceof Error ? error.message : 'Unknown error'}`;
    }
  },
  {
    name: "read_file",
    description: "Read the content of a file",
    schema: z.object({
      path: z.string().describe("File path to read"),
    }),
  }
);

export const listFilesTool = tool(
  async () => {
    try {
      const files = FileManager.getFiles();
      return files.map(file => ({
        name: file.name,
        id: file.id,
        createdAt: file.createdAt.toISOString(),
        modifiedAt: file.modifiedAt.toISOString(),
        size: file.html.length
      }));
    } catch (error) {
      return [];
    }
  },
  {
    name: "list_files",
    description: "List all files in the project",
    schema: z.object({}),
  }
);

// Agent handoff tools
export const handoffToAgentTool = (targetAgent: string) => tool(
  async (input: { reason: string; data?: any }) => {
    // const state = getCurrentTaskInput() as AgentWorkflowStateType;
    
    return new Command({
      goto: targetAgent,
      update: {
        activeAgent: targetAgent as any,
        messages: [{
          role: "assistant",
          content: `Handing off to ${targetAgent}: ${input.reason}`,
        }],
      },
    });
  },
  {
    name: `handoff_to_${targetAgent}`,
    description: `Hand off control to the ${targetAgent} agent`,
    schema: z.object({
      reason: z.string().describe("Reason for the handoff"),
      data: z.any().optional().describe("Optional data to pass to the next agent"),
    }),
  }
);

// Content generation tools
export const generateContentTool = tool(
  async (input: {
    type: string;
    requirements: string;
    tone?: string;
    length?: string;
  }) => {
    try {
      // For now, return a simple string response
      // In a full implementation, this would call the AI API
      return `Generated ${input.type} content based on: ${input.requirements}${input.tone ? ` (Tone: ${input.tone})` : ''}${input.length ? ` (Length: ${input.length})` : ''}`;
    } catch (error) {
      return `Error generating content: ${error instanceof Error ? error.message : 'Unknown error'}`;
    }
  },
  {
    name: "generate_content",
    description: "Generate content for the website",
    schema: z.object({
      type: z.enum(["headline", "paragraph", "list", "cta", "navigation"]).describe("Type of content to generate"),
      requirements: z.string().describe("Content requirements and context"),
      tone: z.enum(["formal", "casual", "friendly", "professional", "creative"]).optional(),
      length: z.enum(["short", "medium", "long"]).optional(),
    }),
  }
);

// Design tools
export const generateColorSchemeTool = tool(
  async (input: { style: string; mood?: string }) => {
    // Generate color scheme based on style and mood
    const colorSchemes = {
      modern: ["#2563eb", "#1e40af", "#f8fafc", "#64748b"],
      classic: ["#1f2937", "#374151", "#f9fafb", "#6b7280"],
      minimal: ["#000000", "#ffffff", "#f5f5f5", "#9ca3af"],
      creative: ["#7c3aed", "#a855f7", "#fbbf24", "#f59e0b"],
      professional: ["#1e40af", "#3b82f6", "#f1f5f9", "#475569"],
    };

    return colorSchemes[input.style as keyof typeof colorSchemes] || colorSchemes.modern;
  },
  {
    name: "generate_color_scheme",
    description: "Generate a color scheme for the website",
    schema: z.object({
      style: z.enum(["modern", "classic", "minimal", "creative", "professional"]),
      mood: z.string().optional().describe("Desired mood or feeling"),
    }),
  }
);

// Code validation tools
export const validateHTMLTool = tool(
  async (input: { html: string }) => {
    // Basic HTML validation
    const hasDoctype = input.html.includes("<!DOCTYPE");
    const hasHtmlTag = input.html.includes("<html");
    const hasHeadTag = input.html.includes("<head");
    const hasBodyTag = input.html.includes("<body");
    const hasClosingTags = input.html.includes("</html>");

    const issues = [];
    if (!hasDoctype) issues.push("Missing DOCTYPE declaration");
    if (!hasHtmlTag) issues.push("Missing <html> tag");
    if (!hasHeadTag) issues.push("Missing <head> tag");
    if (!hasBodyTag) issues.push("Missing <body> tag");
    if (!hasClosingTags) issues.push("Missing closing tags");

    return {
      isValid: issues.length === 0,
      issues,
      score: Math.max(0, 100 - (issues.length * 20)),
    };
  },
  {
    name: "validate_html",
    description: "Validate HTML structure and syntax",
    schema: z.object({
      html: z.string().describe("HTML content to validate"),
    }),
  }
);

// Progress tracking tools
export const updateProgressTool = tool(
  async (input: { step: string; completed: number; total: number }) => {
    return new Command({
      update: {
        progress: {
          completed: input.completed,
          total: input.total,
          currentStep: input.step,
        },
      },
    });
  },
  {
    name: "update_progress",
    description: "Update workflow progress",
    schema: z.object({
      step: z.string().describe("Current step description"),
      completed: z.number().describe("Number of completed steps"),
      total: z.number().describe("Total number of steps"),
    }),
  }
);

// Request approval tool
export const requestApprovalTool = tool(
  async (input: { reason: string; data?: any }) => {
    return new Command({
      update: {
        requiresApproval: true,
        messages: [{
          role: "assistant",
          content: `Requesting approval: ${input.reason}`,
        }],
      },
    });
  },
  {
    name: "request_approval",
    description: "Request user approval before proceeding",
    schema: z.object({
      reason: z.string().describe("Reason for requesting approval"),
      data: z.any().optional().describe("Data to present for approval"),
    }),
  }
);

// Export all tools
export const agentTools = {
  // File system
  createFileTool,
  updateFileContentTool,
  readFileTool,
  listFilesTool,
  
  // Content generation
  generateContentTool,
  
  // Design
  generateColorSchemeTool,
  
  // Code validation
  validateHTMLTool,
  
  // Progress tracking
  updateProgressTool,
  requestApprovalTool,
  
  // Handoff tools (created dynamically)
  handoffToAgentTool,
};
