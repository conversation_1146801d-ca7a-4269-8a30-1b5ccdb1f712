import { Chat<PERSON><PERSON>A<PERSON> } from "@langchain/openai";
import { ChatAnthropic } from "@langchain/anthropic";
import { createReactAgent } from "@langchain/langgraph/prebuilt";
import { AgentConfig, AgentWorkflowStateType, WebsiteRequirements } from "./types";
import { 
  generateColorSchemeTool,
  createFileTool,
  readFileTool,
  handoffToAgentTool,
  updateProgressTool,
  requestApprovalTool 
} from "./tools";

export class DesignAgent {
  private agent: any;
  private config: AgentConfig;

  constructor(config: Partial<AgentConfig> = {}) {
    this.config = {
      name: "Design Agent",
      type: "design",
      description: "Creates visual design decisions including layout, colors, typography, and styling",
      capabilities: [
        "Generate color schemes and palettes",
        "Design layout structures and wireframes",
        "Select appropriate typography",
        "Create responsive design patterns",
        "Ensure visual consistency",
        "Apply modern design principles"
      ],
      tools: ["generate_color_scheme", "create_file", "read_file", "handoff_to_code_generation", "update_progress"],
      model: "gpt-4o",
      provider: "openai",
      systemPrompt: this.getSystemPrompt(),
      ...config,
    };

    this.initializeAgent();
  }

  private getSystemPrompt(): string {
    return `You are a Design Agent specialized in creating visual design systems for websites.

Your responsibilities:
1. Analyze content and requirements to create appropriate visual designs
2. Generate color schemes that match the website style and mood
3. Design layout structures and component arrangements
4. Select typography that enhances readability and brand
5. Create responsive design patterns for all devices
6. Ensure accessibility and usability standards

Design Principles:
- Follow modern web design best practices
- Ensure visual hierarchy and clear information architecture
- Create designs that support the content and user goals
- Maintain consistency across all design elements
- Consider accessibility (contrast, readability, navigation)
- Design for mobile-first responsive layouts

Available tools:
- generate_color_scheme: Create color palettes for the website
- create_file: Save design specifications and CSS files
- read_file: Review existing content and files
- handoff_to_code_generation: Pass control to the code generation agent
- update_progress: Update workflow progress
- request_approval: Request user approval for design decisions

When you complete the design phase, hand off to the code generation agent to implement the designs.`;
  }

  private initializeAgent() {
    const model = this.config.provider === "anthropic" 
      ? new ChatAnthropic({ model: this.config.model || "claude-3-sonnet-20240229" })
      : new ChatOpenAI({ model: this.config.model || "gpt-4o" });

    const tools = [
      generateColorSchemeTool,
      createFileTool,
      readFileTool,
      handoffToAgentTool("code_generation"),
      updateProgressTool,
      requestApprovalTool,
    ];

    this.agent = createReactAgent({
      llm: model,
      tools,
      stateModifier: this.config.systemPrompt,
    });
  }

  async execute(state: AgentWorkflowStateType): Promise<any> {
    const requirements = state.requirements;
    
    // Update progress
    await updateProgressTool.invoke({
      step: "Creating visual design and layout",
      completed: 2,
      total: 5,
    });

    // Prepare design prompt
    const designPrompt = this.buildDesignPrompt(requirements, state);
    
    // Execute the agent
    const result = await this.agent.invoke({
      messages: [
        ...state.messages,
        {
          role: "user",
          content: designPrompt,
        }
      ],
    });

    return result;
  }

  private buildDesignPrompt(requirements: WebsiteRequirements, state: AgentWorkflowStateType): string {
    const { description, type, style, colorScheme, features } = requirements;
    
    // Get existing content files to inform design decisions
    const contentFiles = state.files.filter(f => f.type === "txt" || f.type === "md");
    const contentSummary = contentFiles.length > 0 
      ? `\nExisting content files: ${contentFiles.map(f => f.path).join(", ")}`
      : "";

    return `Create a visual design system for a ${type} website with the following requirements:

Description: ${description}
Style: ${style}
Color Scheme: ${colorScheme || "Generate appropriate colors"}
Features: ${features.join(", ")}
${contentSummary}

Please create:
1. Color palette (primary, secondary, accent, neutral colors)
2. Typography system (headings, body text, special text)
3. Layout structure and grid system
4. Component design patterns (buttons, cards, forms, navigation)
5. Responsive breakpoints and mobile design
6. Spacing and sizing system
7. Visual hierarchy guidelines

Generate design specification files including:
- design-system.json: Complete design system specifications
- styles.css: Base CSS with design tokens and utility classes
- layout-guide.md: Layout and component usage guidelines

Ensure the design supports the website type and enhances the user experience.`;
  }

  getConfig(): AgentConfig {
    return this.config;
  }

  async generateColorPalette(style: string, mood?: string): Promise<string[]> {
    return await generateColorSchemeTool.invoke({
      style: style as any,
      mood,
    });
  }

  async createDesignSystem(requirements: WebsiteRequirements): Promise<any> {
    const colors = await this.generateColorPalette(requirements.style);
    
    const designSystem = {
      colors: {
        primary: colors[0],
        secondary: colors[1],
        background: colors[2],
        text: colors[3],
      },
      typography: this.getTypographySystem(requirements.style),
      spacing: this.getSpacingSystem(),
      breakpoints: this.getBreakpoints(),
      components: this.getComponentStyles(requirements.type),
    };

    return designSystem;
  }

  private getTypographySystem(style: string): any {
    const typographySystems = {
      modern: {
        headings: "font-family: 'Inter', sans-serif; font-weight: 600;",
        body: "font-family: 'Inter', sans-serif; font-weight: 400;",
        sizes: {
          h1: "2.5rem",
          h2: "2rem", 
          h3: "1.5rem",
          body: "1rem",
          small: "0.875rem",
        }
      },
      classic: {
        headings: "font-family: 'Georgia', serif; font-weight: 700;",
        body: "font-family: 'Georgia', serif; font-weight: 400;",
        sizes: {
          h1: "2.25rem",
          h2: "1.875rem",
          h3: "1.5rem", 
          body: "1rem",
          small: "0.875rem",
        }
      },
      minimal: {
        headings: "font-family: 'Helvetica', sans-serif; font-weight: 300;",
        body: "font-family: 'Helvetica', sans-serif; font-weight: 300;",
        sizes: {
          h1: "2rem",
          h2: "1.5rem",
          h3: "1.25rem",
          body: "1rem", 
          small: "0.875rem",
        }
      },
      creative: {
        headings: "font-family: 'Poppins', sans-serif; font-weight: 700;",
        body: "font-family: 'Open Sans', sans-serif; font-weight: 400;",
        sizes: {
          h1: "3rem",
          h2: "2.25rem",
          h3: "1.75rem",
          body: "1rem",
          small: "0.875rem",
        }
      },
      professional: {
        headings: "font-family: 'Roboto', sans-serif; font-weight: 500;",
        body: "font-family: 'Roboto', sans-serif; font-weight: 400;",
        sizes: {
          h1: "2.25rem",
          h2: "1.875rem", 
          h3: "1.5rem",
          body: "1rem",
          small: "0.875rem",
        }
      },
    };

    return typographySystems[style as keyof typeof typographySystems] || typographySystems.modern;
  }

  private getSpacingSystem(): any {
    return {
      xs: "0.25rem",
      sm: "0.5rem", 
      md: "1rem",
      lg: "1.5rem",
      xl: "2rem",
      "2xl": "3rem",
      "3xl": "4rem",
    };
  }

  private getBreakpoints(): any {
    return {
      sm: "640px",
      md: "768px", 
      lg: "1024px",
      xl: "1280px",
      "2xl": "1536px",
    };
  }

  private getComponentStyles(websiteType: string): any {
    const baseComponents = {
      button: {
        primary: "px-6 py-3 rounded-lg font-medium transition-colors",
        secondary: "px-6 py-3 rounded-lg border font-medium transition-colors",
      },
      card: {
        base: "rounded-lg shadow-md p-6 bg-white",
        hover: "hover:shadow-lg transition-shadow",
      },
      navigation: {
        base: "flex items-center justify-between p-4",
        mobile: "md:hidden",
        desktop: "hidden md:flex",
      },
    };

    // Customize based on website type
    const typeSpecificStyles = {
      ecommerce: {
        productCard: "border rounded-lg p-4 hover:shadow-md transition-shadow",
        priceTag: "font-bold text-lg text-primary",
      },
      portfolio: {
        projectCard: "group relative overflow-hidden rounded-lg",
        overlay: "absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity",
      },
      blog: {
        articleCard: "border-b pb-6 mb-6",
        readMore: "text-primary hover:underline",
      },
    };

    return {
      ...baseComponents,
      ...typeSpecificStyles[websiteType as keyof typeof typeSpecificStyles],
    };
  }
}
