import { StateGraph, START, END, Command } from "@langchain/langgraph";
import { MemorySaver } from "@langchain/langgraph";
import { AgentWorkflowState, AgentWorkflowStateType, WebsiteRequirements, WorkflowContext } from "./types";
import { ContentGenerationAgent } from "./content-generation-agent";
import { DesignAgent } from "./design-agent";
import { CodeGenerationAgent } from "./code-generation-agent";
import { FileManagementAgent } from "./file-management-agent";
import { QualityAssuranceAgent } from "./quality-assurance-agent";
import { SupervisorAgent } from "./supervisor-agent";

export class AgentWorkflow {
  private graph: any;
  private memory: MemorySaver;
  private agents: {
    supervisor: SupervisorAgent;
    contentGeneration: ContentGenerationAgent;
    design: DesignAgent;
    codeGeneration: CodeGenerationAgent;
    fileManagement: FileManagementAgent;
    qualityAssurance: QualityAssuranceAgent;
  } = {} as any;

  constructor() {
    this.memory = new MemorySaver();
    this.initializeAgents();
    this.buildWorkflow();
  }

  private initializeAgents() {
    this.agents = {
      supervisor: new SupervisorAgent(),
      contentGeneration: new ContentGenerationAgent(),
      design: new DesignAgent(),
      codeGeneration: new CodeGenerationAgent(),
      fileManagement: new FileManagementAgent(),
      qualityAssurance: new QualityAssuranceAgent(),
    };
  }

  private buildWorkflow() {
    // Define workflow nodes
    const supervisorNode = async (state: AgentWorkflowStateType) => {
      const result = await this.agents.supervisor.execute(state);
      return result;
    };

    const contentGenerationNode = async (state: AgentWorkflowStateType) => {
      const result = await this.agents.contentGeneration.execute(state);
      return new Command({
        goto: "supervisor",
        update: {
          activeAgent: "content_generation",
          messages: result.messages || [],
        },
      });
    };

    const designNode = async (state: AgentWorkflowStateType) => {
      const result = await this.agents.design.execute(state);
      return new Command({
        goto: "supervisor", 
        update: {
          activeAgent: "design",
          messages: result.messages || [],
        },
      });
    };

    const codeGenerationNode = async (state: AgentWorkflowStateType) => {
      const result = await this.agents.codeGeneration.execute(state);
      return new Command({
        goto: "supervisor",
        update: {
          activeAgent: "code_generation", 
          messages: result.messages || [],
        },
      });
    };

    const fileManagementNode = async (state: AgentWorkflowStateType) => {
      const result = await this.agents.fileManagement.execute(state);
      return new Command({
        goto: "supervisor",
        update: {
          activeAgent: "file_management",
          messages: result.messages || [],
        },
      });
    };

    const qualityAssuranceNode = async (state: AgentWorkflowStateType) => {
      const result = await this.agents.qualityAssurance.execute(state);
      return new Command({
        goto: END,
        update: {
          activeAgent: "quality_assurance",
          status: "completed",
          messages: result.messages || [],
        },
      });
    };

    // Conditional routing function
    const routeFromSupervisor = (state: AgentWorkflowStateType) => {
      const nextAgent = this.agents.supervisor.determineNextAgent(state);
      
      if (!nextAgent) {
        return END;
      }
      
      switch (nextAgent) {
        case "content_generation":
          return "content_generation";
        case "design":
          return "design";
        case "code_generation":
          return "code_generation";
        case "file_management":
          return "file_management";
        case "quality_assurance":
          return "quality_assurance";
        default:
          return END;
      }
    };

    // Build the state graph
    this.graph = new StateGraph(AgentWorkflowState)
      .addNode("supervisor", supervisorNode, {
        ends: ["content_generation", "design", "code_generation", "file_management", "quality_assurance", END],
      })
      .addNode("content_generation", contentGenerationNode, {
        ends: ["supervisor"],
      })
      .addNode("design", designNode, {
        ends: ["supervisor"],
      })
      .addNode("code_generation", codeGenerationNode, {
        ends: ["supervisor"],
      })
      .addNode("file_management", fileManagementNode, {
        ends: ["supervisor"],
      })
      .addNode("quality_assurance", qualityAssuranceNode, {
        ends: [END],
      })
      .addEdge(START, "supervisor")
      .addConditionalEdges("supervisor", routeFromSupervisor)
      .compile({
        checkpointer: this.memory,
      });
  }

  async executeWorkflow(
    requirements: WebsiteRequirements,
    context: WorkflowContext
  ): Promise<{
    success: boolean;
    files: any[];
    messages: any[];
    errors: string[];
    finalState: AgentWorkflowStateType;
  }> {
    try {
      // Initialize workflow state
      const initialState: Partial<AgentWorkflowStateType> = {
        requirements,
        tasks: this.agents.supervisor.createProjectPlan(requirements),
        status: "planning",
        progress: {
          completed: 0,
          total: 5,
          currentStep: "Initializing workflow",
        },
        messages: [],
      };

      // Execute the workflow
      const result = await this.graph.invoke(initialState, {
        configurable: {
          thread_id: context.sessionId,
          user_id: context.userId,
        },
      });

      return {
        success: result.status === "completed",
        files: result.files || [],
        messages: result.messages || [],
        errors: result.errors || [],
        finalState: result,
      };
    } catch (error) {
      console.error("Workflow execution error:", error);
      return {
        success: false,
        files: [],
        messages: [],
        errors: [error instanceof Error ? error.message : "Unknown error"],
        finalState: {} as AgentWorkflowStateType,
      };
    }
  }

  async streamWorkflow(
    requirements: WebsiteRequirements,
    context: WorkflowContext,
    onUpdate?: (update: any) => void
  ): Promise<AsyncGenerator<any, void, unknown>> {
    const initialState: Partial<AgentWorkflowStateType> = {
      requirements,
      tasks: this.agents.supervisor.createProjectPlan(requirements),
      status: "planning",
      progress: {
        completed: 0,
        total: 5,
        currentStep: "Initializing workflow",
      },
      messages: [],
    };

    const stream = this.graph.stream(initialState, {
      configurable: {
        thread_id: context.sessionId,
        user_id: context.userId,
      },
    });

    return this.processStream(stream, onUpdate);
  }

  private async* processStream(
    stream: AsyncGenerator<any, void, unknown>,
    onUpdate?: (update: any) => void
  ): AsyncGenerator<any, void, unknown> {
    try {
      for await (const chunk of stream) {
        // Process and format the chunk
        const update = this.formatStreamUpdate(chunk);
        
        // Call update callback if provided
        if (onUpdate) {
          onUpdate(update);
        }
        
        yield update;
      }
    } catch (error) {
      console.error("Stream processing error:", error);
      const errorUpdate = {
        type: "error",
        error: error instanceof Error ? error.message : "Unknown error",
        timestamp: new Date().toISOString(),
      };
      
      if (onUpdate) {
        onUpdate(errorUpdate);
      }
      
      yield errorUpdate;
    }
  }

  private formatStreamUpdate(chunk: any): any {
    // Extract relevant information from the chunk
    const update = {
      type: "progress",
      agent: chunk.activeAgent || "unknown",
      status: chunk.status || "processing",
      progress: chunk.progress || { completed: 0, total: 5, currentStep: "Processing" },
      messages: chunk.messages || [],
      files: chunk.files || [],
      errors: chunk.errors || [],
      requiresApproval: chunk.requiresApproval || false,
      timestamp: new Date().toISOString(),
    };

    return update;
  }

  async getWorkflowState(sessionId: string): Promise<AgentWorkflowStateType | null> {
    try {
      const state = await this.graph.getState({
        configurable: { thread_id: sessionId },
      });
      
      return state?.values || null;
    } catch (error) {
      console.error("Error getting workflow state:", error);
      return null;
    }
  }

  async updateWorkflowState(
    sessionId: string,
    updates: Partial<AgentWorkflowStateType>
  ): Promise<boolean> {
    try {
      await this.graph.updateState(
        { configurable: { thread_id: sessionId } },
        updates
      );
      
      return true;
    } catch (error) {
      console.error("Error updating workflow state:", error);
      return false;
    }
  }

  async approveWorkflow(sessionId: string, approved: boolean, feedback?: string): Promise<boolean> {
    try {
      const updates = {
        requiresApproval: false,
        status: approved ? ("executing" as const) : ("failed" as const),
        feedback: feedback || "",
      };

      return await this.updateWorkflowState(sessionId, updates);
    } catch (error) {
      console.error("Error approving workflow:", error);
      return false;
    }
  }

  async pauseWorkflow(sessionId: string): Promise<boolean> {
    try {
      const updates = {
        status: "paused" as const,
      };
      
      return await this.updateWorkflowState(sessionId, updates);
    } catch (error) {
      console.error("Error pausing workflow:", error);
      return false;
    }
  }

  async resumeWorkflow(sessionId: string): Promise<boolean> {
    try {
      const updates = {
        status: "executing" as const,
      };
      
      return await this.updateWorkflowState(sessionId, updates);
    } catch (error) {
      console.error("Error resuming workflow:", error);
      return false;
    }
  }

  getAgentConfigs() {
    return {
      supervisor: this.agents.supervisor.getConfig(),
      contentGeneration: this.agents.contentGeneration.getConfig(),
      design: this.agents.design.getConfig(),
      codeGeneration: this.agents.codeGeneration.getConfig(),
      fileManagement: this.agents.fileManagement.getConfig(),
      qualityAssurance: this.agents.qualityAssurance.getConfig(),
    };
  }
}
