import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "@langchain/openai";
import { ChatAnthropic } from "@langchain/anthropic";
import { createReactAgent } from "@langchain/langgraph/prebuilt";
import { AgentConfig, AgentWorkflowStateType, ProjectFile } from "./types";
import { 
  createFileTool,
  readFileTool,
  updateFileContentTool,
  listFilesTool,
  handoffToAgentTool,
  updateProgressTool,
  requestApprovalTool 
} from "./tools";

export class FileManagementAgent {
  private agent: any;
  private config: AgentConfig;

  constructor(config: Partial<AgentConfig> = {}) {
    this.config = {
      name: "File Management Agent",
      type: "file_management",
      description: "Organizes project files, manages file structure, and ensures proper project organization",
      capabilities: [
        "Organize project file structure",
        "Manage file naming conventions",
        "Create project documentation",
        "Optimize file organization",
        "Ensure file consistency",
        "Generate project manifests"
      ],
      tools: ["create_file", "read_file", "update_file_content", "list_files", "handoff_to_quality_assurance", "update_progress"],
      model: "gpt-4o",
      provider: "openai",
      systemPrompt: this.getSystemPrompt(),
      ...config,
    };

    this.initializeAgent();
  }

  private getSystemPrompt(): string {
    return `You are a File Management Agent specialized in organizing and managing website project files.

Your responsibilities:
1. Organize project files in a logical, maintainable structure
2. Ensure consistent file naming conventions
3. Create proper project documentation (README, file descriptions)
4. Optimize file organization for development and deployment
5. Generate project manifests and file listings
6. Ensure all necessary files are present and properly structured

File Organization Principles:
- Use clear, descriptive file names
- Organize files by type and function
- Create logical folder structures
- Include proper documentation
- Ensure files are optimized for web delivery
- Follow web development best practices

Available tools:
- create_file: Create new project files and documentation
- read_file: Read and analyze existing files
- update_file_content: Update file contents for optimization
- list_files: Get overview of all project files
- handoff_to_quality_assurance: Pass control to QA agent for final review
- update_progress: Update workflow progress
- request_approval: Request user approval for file organization

When you complete file organization, hand off to the quality assurance agent for final validation.`;
  }

  private initializeAgent() {
    const model = this.config.provider === "anthropic" 
      ? new ChatAnthropic({ model: this.config.model || "claude-3-sonnet-20240229" })
      : new ChatOpenAI({ model: this.config.model || "gpt-4o" });

    const tools = [
      createFileTool,
      readFileTool,
      updateFileContentTool,
      listFilesTool,
      handoffToAgentTool("quality_assurance"),
      updateProgressTool,
      requestApprovalTool,
    ];

    this.agent = createReactAgent({
      llm: model,
      tools,
      stateModifier: this.config.systemPrompt,
    });
  }

  async execute(state: AgentWorkflowStateType): Promise<any> {
    const requirements = state.requirements;
    
    // Update progress
    await updateProgressTool.invoke({
      step: "Organizing project files and structure",
      completed: 4,
      total: 5,
    });

    // Prepare file management prompt
    const filePrompt = this.buildFileManagementPrompt(requirements, state);
    
    // Execute the agent
    const result = await this.agent.invoke({
      messages: [
        ...state.messages,
        {
          role: "user",
          content: filePrompt,
        }
      ],
    });

    return result;
  }

  private buildFileManagementPrompt(requirements: any, state: AgentWorkflowStateType): string {
    const existingFiles = state.files;
    const fileList = existingFiles.map(f => `${f.path} (${f.type})`).join("\n");

    return `Organize and manage the project files for a ${requirements.type} website.

Current files:
${fileList || "No files yet"}

Please:
1. Review the current file structure and organization
2. Create any missing essential files (README.md, .gitignore, etc.)
3. Ensure proper file naming conventions
4. Optimize file organization for web deployment
5. Create project documentation
6. Generate a project manifest/file listing

Required project structure:
- index.html (main HTML file)
- styles.css (main stylesheet)
- script.js (main JavaScript file, if needed)
- README.md (project documentation)
- assets/ folder (for images, fonts, etc.)
- Any additional files based on website type

Ensure all files are properly organized and documented for easy maintenance and deployment.`;
  }

  getConfig(): AgentConfig {
    return this.config;
  }

  async organizeProjectStructure(files: ProjectFile[]): Promise<ProjectFile[]> {
    const organizedFiles: ProjectFile[] = [];
    
    // Ensure core files exist
    const coreFiles = this.ensureCoreFiles(files);
    organizedFiles.push(...coreFiles);
    
    // Organize existing files
    const organizedExisting = this.organizeExistingFiles(files);
    organizedFiles.push(...organizedExisting);
    
    // Add documentation files
    const docFiles = this.createDocumentationFiles(files);
    organizedFiles.push(...docFiles);
    
    return organizedFiles;
  }

  private ensureCoreFiles(files: ProjectFile[]): ProjectFile[] {
    const coreFiles: ProjectFile[] = [];
    
    // Check for index.html
    if (!files.find(f => f.path === "index.html")) {
      coreFiles.push({
        path: "index.html",
        content: "<!-- Main HTML file will be generated -->",
        type: "html",
        description: "Main HTML file for the website",
      });
    }
    
    // Check for styles.css
    if (!files.find(f => f.path === "styles.css")) {
      coreFiles.push({
        path: "styles.css",
        content: "/* Main stylesheet */",
        type: "css",
        description: "Main CSS stylesheet",
      });
    }
    
    return coreFiles;
  }

  private organizeExistingFiles(files: ProjectFile[]): ProjectFile[] {
    // Apply naming conventions and organization rules
    return files.map(file => {
      // Ensure proper file extensions
      let path = file.path;
      if (file.type === "html" && !path.endsWith(".html")) {
        path += ".html";
      } else if (file.type === "css" && !path.endsWith(".css")) {
        path += ".css";
      } else if (file.type === "js" && !path.endsWith(".js")) {
        path += ".js";
      }
      
      return {
        ...file,
        path,
      };
    });
  }

  private createDocumentationFiles(files: ProjectFile[]): ProjectFile[] {
    const docFiles: ProjectFile[] = [];
    
    // Create README.md
    const readmeContent = this.generateReadmeContent(files);
    docFiles.push({
      path: "README.md",
      content: readmeContent,
      type: "md",
      description: "Project documentation and setup instructions",
    });
    
    // Create .gitignore
    const gitignoreContent = this.generateGitignoreContent();
    docFiles.push({
      path: ".gitignore",
      content: gitignoreContent,
      type: "txt",
      description: "Git ignore file for version control",
    });
    
    // Create project manifest
    const manifestContent = this.generateProjectManifest(files);
    docFiles.push({
      path: "project-manifest.json",
      content: manifestContent,
      type: "json",
      description: "Project file manifest and metadata",
    });
    
    return docFiles;
  }

  private generateReadmeContent(files: ProjectFile[]): string {
    const fileList = files.map(f => `- ${f.path} - ${f.description || f.type + " file"}`).join("\n");
    
    return `# Website Project

This is a website project generated by DeepSite AI agents.

## Project Structure

${fileList}

## Setup Instructions

1. Open \`index.html\` in a web browser to view the website
2. For development, use a local web server
3. Customize styles in \`styles.css\`
4. Add interactivity in \`script.js\`

## Deployment

This project is ready for deployment to any static hosting service:
- GitHub Pages
- Netlify
- Vercel
- AWS S3
- Any web server

## Technologies Used

- HTML5
- CSS3
- JavaScript (ES6+)
- Tailwind CSS (via CDN)

## Browser Support

This website supports all modern browsers including:
- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## License

This project is open source and available under the MIT License.
`;
  }

  private generateGitignoreContent(): string {
    return `# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build outputs
dist/
build/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# Temporary folders
tmp/
temp/
`;
  }

  private generateProjectManifest(files: ProjectFile[]): string {
    const manifest = {
      name: "DeepSite Generated Website",
      version: "1.0.0",
      description: "Website generated by DeepSite AI agents",
      generatedAt: new Date().toISOString(),
      files: files.map(f => ({
        path: f.path,
        type: f.type,
        description: f.description,
        size: f.content.length,
      })),
      structure: {
        totalFiles: files.length,
        htmlFiles: files.filter(f => f.type === "html").length,
        cssFiles: files.filter(f => f.type === "css").length,
        jsFiles: files.filter(f => f.type === "js").length,
        otherFiles: files.filter(f => !["html", "css", "js"].includes(f.type)).length,
      },
      deployment: {
        ready: true,
        entryPoint: "index.html",
        requirements: ["Modern web browser", "HTTP server (for development)"],
      },
    };
    
    return JSON.stringify(manifest, null, 2);
  }

  async validateFileStructure(files: ProjectFile[]): Promise<{
    isValid: boolean;
    issues: string[];
    recommendations: string[];
  }> {
    const issues: string[] = [];
    const recommendations: string[] = [];
    
    // Check for required files
    if (!files.find(f => f.path === "index.html")) {
      issues.push("Missing index.html file");
    }
    
    if (!files.find(f => f.type === "css")) {
      recommendations.push("Consider adding CSS for styling");
    }
    
    // Check file naming conventions
    files.forEach(file => {
      if (file.path.includes(" ")) {
        issues.push(`File name contains spaces: ${file.path}`);
      }
      
      if (file.path !== file.path.toLowerCase()) {
        recommendations.push(`Consider using lowercase for file names: ${file.path}`);
      }
    });
    
    return {
      isValid: issues.length === 0,
      issues,
      recommendations,
    };
  }
}
