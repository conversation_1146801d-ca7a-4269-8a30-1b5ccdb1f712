import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "@langchain/openai";
import { ChatAnthropic } from "@langchain/anthropic";
import { createReactAgent } from "@langchain/langgraph/prebuilt";
import { AgentConfig, AgentWorkflowStateType, WebsiteRequirements } from "./types";
import { 
  createFileTool,
  readFileTool,
  updateFileContentTool,
  validateHTMLTool,
  handoffToAgentTool,
  updateProgressTool,
  requestApprovalTool 
} from "./tools";

export class CodeGenerationAgent {
  private agent: any;
  private config: AgentConfig;

  constructor(config: Partial<AgentConfig> = {}) {
    this.config = {
      name: "Code Generation Agent",
      type: "code_generation",
      description: "Generates HTML, CSS, and JavaScript code based on content and design specifications",
      capabilities: [
        "Generate semantic HTML structure",
        "Create responsive CSS layouts",
        "Implement interactive JavaScript features",
        "Ensure cross-browser compatibility",
        "Optimize code for performance",
        "Follow web standards and best practices"
      ],
      tools: ["create_file", "read_file", "update_file_content", "validate_html", "handoff_to_file_management", "update_progress"],
      model: "gpt-4o",
      provider: "openai",
      systemPrompt: this.getSystemPrompt(),
      ...config,
    };

    this.initializeAgent();
  }

  private getSystemPrompt(): string {
    return `You are a Code Generation Agent specialized in creating high-quality web code.

Your responsibilities:
1. Generate semantic, accessible HTML structure
2. Create responsive CSS using modern techniques (Flexbox, Grid, CSS Variables)
3. Implement interactive JavaScript features when needed
4. Ensure cross-browser compatibility and web standards compliance
5. Optimize code for performance and maintainability
6. Include proper meta tags, SEO elements, and accessibility features

Coding Standards:
- Use semantic HTML5 elements
- Write clean, well-commented code
- Implement responsive design with mobile-first approach
- Use CSS custom properties for design tokens
- Follow accessibility guidelines (WCAG 2.1)
- Optimize images and assets
- Include proper meta tags for SEO
- Use modern JavaScript (ES6+) when needed

Available tools:
- create_file: Create new HTML, CSS, or JS files
- read_file: Read existing content and design files
- update_file_content: Update existing code files
- validate_html: Validate HTML structure and syntax
- handoff_to_file_management: Pass control to file management agent
- update_progress: Update workflow progress
- request_approval: Request user approval for code implementation

When you complete code generation, hand off to the file management agent for organization and final review.`;
  }

  private initializeAgent() {
    const model = this.config.provider === "anthropic" 
      ? new ChatAnthropic({ model: this.config.model || "claude-3-sonnet-20240229" })
      : new ChatOpenAI({ model: this.config.model || "gpt-4o" });

    const tools = [
      createFileTool,
      readFileTool,
      updateFileContentTool,
      validateHTMLTool,
      handoffToAgentTool("file_management"),
      updateProgressTool,
      requestApprovalTool,
    ];

    this.agent = createReactAgent({
      llm: model,
      tools,
      stateModifier: this.config.systemPrompt,
    });
  }

  async execute(state: AgentWorkflowStateType): Promise<any> {
    const requirements = state.requirements;
    
    // Update progress
    await updateProgressTool.invoke({
      step: "Generating HTML, CSS, and JavaScript code",
      completed: 3,
      total: 5,
    });

    // Prepare code generation prompt
    const codePrompt = this.buildCodePrompt(requirements, state);
    
    // Execute the agent
    const result = await this.agent.invoke({
      messages: [
        ...state.messages,
        {
          role: "user",
          content: codePrompt,
        }
      ],
    });

    return result;
  }

  private buildCodePrompt(requirements: WebsiteRequirements, state: AgentWorkflowStateType): string {
    const { description, type, style, features } = requirements;
    
    // Get existing content and design files
    const contentFiles = state.files.filter(f => f.type === "txt" || f.type === "md");
    const designFiles = state.files.filter(f => f.type === "json" || f.path.includes("design"));
    
    const existingAssets = [
      ...contentFiles.map(f => `Content: ${f.path}`),
      ...designFiles.map(f => `Design: ${f.path}`)
    ].join("\n");

    return `Generate complete, production-ready code for a ${type} website with the following requirements:

Description: ${description}
Style: ${style}
Features: ${features.join(", ")}

Existing Assets:
${existingAssets || "No existing assets - create from scratch"}

Please generate:
1. index.html - Complete HTML structure with:
   - Proper DOCTYPE and meta tags
   - Semantic HTML5 elements
   - Accessibility attributes
   - SEO-optimized structure
   - Responsive viewport meta tag

2. styles.css - Complete CSS with:
   - CSS custom properties for design tokens
   - Responsive layout using CSS Grid/Flexbox
   - Mobile-first media queries
   - Modern CSS techniques
   - Cross-browser compatibility

3. script.js (if needed) - JavaScript for:
   - Interactive features
   - Form handling
   - Navigation functionality
   - Performance optimizations

Requirements:
- Use Tailwind CSS classes where appropriate
- Ensure full responsiveness (mobile, tablet, desktop)
- Include proper meta tags for SEO
- Implement accessibility best practices
- Optimize for performance
- Follow modern web standards

Create a complete, functional website that matches the design specifications and content requirements.`;
  }

  getConfig(): AgentConfig {
    return this.config;
  }

  async generateHTML(requirements: WebsiteRequirements, content: any, _design: any): Promise<string> {
    const htmlTemplate = this.getHTMLTemplate(requirements.type);
    
    // Customize template based on requirements
    return this.customizeHTMLTemplate(htmlTemplate, requirements, content, _design);
  }

  private getHTMLTemplate(websiteType: string): string {
    const templates = {
      landing_page: `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{TITLE}}</title>
    <meta name="description" content="{{DESCRIPTION}}">
    <link rel="stylesheet" href="styles.css">
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body>
    <header class="header">
        <nav class="nav">{{NAVIGATION}}</nav>
    </header>
    <main>
        <section class="hero">{{HERO_CONTENT}}</section>
        <section class="features">{{FEATURES_CONTENT}}</section>
        <section class="cta">{{CTA_CONTENT}}</section>
    </main>
    <footer class="footer">{{FOOTER_CONTENT}}</footer>
    <script src="script.js"></script>
</body>
</html>`,

      portfolio: `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{TITLE}}</title>
    <meta name="description" content="{{DESCRIPTION}}">
    <link rel="stylesheet" href="styles.css">
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body>
    <header class="header">
        <nav class="nav">{{NAVIGATION}}</nav>
    </header>
    <main>
        <section class="hero">{{HERO_CONTENT}}</section>
        <section class="about">{{ABOUT_CONTENT}}</section>
        <section class="portfolio">{{PORTFOLIO_CONTENT}}</section>
        <section class="contact">{{CONTACT_CONTENT}}</section>
    </main>
    <footer class="footer">{{FOOTER_CONTENT}}</footer>
    <script src="script.js"></script>
</body>
</html>`,

      ecommerce: `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{TITLE}}</title>
    <meta name="description" content="{{DESCRIPTION}}">
    <link rel="stylesheet" href="styles.css">
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body>
    <header class="header">
        <nav class="nav">{{NAVIGATION}}</nav>
    </header>
    <main>
        <section class="hero">{{HERO_CONTENT}}</section>
        <section class="products">{{PRODUCTS_CONTENT}}</section>
        <section class="features">{{FEATURES_CONTENT}}</section>
    </main>
    <footer class="footer">{{FOOTER_CONTENT}}</footer>
    <script src="script.js"></script>
</body>
</html>`,
    };

    return templates[websiteType as keyof typeof templates] || templates.landing_page;
  }

  private customizeHTMLTemplate(
    template: string,
    requirements: WebsiteRequirements,
    content: any,
    _design: any
  ): string {
    let html = template;
    
    // Replace placeholders with actual content
    html = html.replace("{{TITLE}}", content?.title || requirements.description);
    html = html.replace("{{DESCRIPTION}}", requirements.description);
    
    // Add more sophisticated content replacement logic here
    // This would integrate with the content and design data
    
    return html;
  }

  async generateCSS(design: any, requirements: WebsiteRequirements): Promise<string> {
    const baseCSS = this.getBaseCSS();
    const customCSS = this.generateCustomCSS(design, requirements);
    
    return `${baseCSS}\n\n${customCSS}`;
  }

  private getBaseCSS(): string {
    return `/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #2563eb;
    --secondary-color: #1e40af;
    --background-color: #ffffff;
    --text-color: #1f2937;
    --border-color: #e5e7eb;
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: var(--background-color);
}

/* Responsive utilities */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

@media (min-width: 768px) {
    .container {
        padding: 0 2rem;
    }
}`;
  }

  private generateCustomCSS(_design: any, requirements: WebsiteRequirements): string {
    // Generate CSS based on design system and requirements
    return `/* Custom styles for ${requirements.type} */
.header {
    background: var(--background-color);
    border-bottom: 1px solid var(--border-color);
    position: sticky;
    top: 0;
    z-index: 100;
}

.hero {
    padding: 4rem 0;
    text-align: center;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
}

.features {
    padding: 4rem 0;
}

.footer {
    background: var(--text-color);
    color: white;
    padding: 2rem 0;
    text-align: center;
}`;
  }

  async generateJavaScript(requirements: WebsiteRequirements): Promise<string> {
    const features = requirements.features;
    let js = "// Website functionality\n\n";
    
    if (features.includes("navigation")) {
      js += this.getNavigationJS();
    }
    
    if (features.includes("forms")) {
      js += this.getFormJS();
    }
    
    if (features.includes("animations")) {
      js += this.getAnimationJS();
    }
    
    return js;
  }

  private getNavigationJS(): string {
    return `// Mobile navigation toggle
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuButton = document.querySelector('[data-mobile-menu-button]');
    const mobileMenu = document.querySelector('[data-mobile-menu]');
    
    if (mobileMenuButton && mobileMenu) {
        mobileMenuButton.addEventListener('click', function() {
            mobileMenu.classList.toggle('hidden');
        });
    }
});

`;
  }

  private getFormJS(): string {
    return `// Form handling
document.addEventListener('DOMContentLoaded', function() {
    const forms = document.querySelectorAll('form');
    
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            // Add form submission logic here
            console.log('Form submitted');
        });
    });
});

`;
  }

  private getAnimationJS(): string {
    return `// Scroll animations
document.addEventListener('DOMContentLoaded', function() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
            }
        });
    }, observerOptions);
    
    document.querySelectorAll('[data-animate]').forEach(el => {
        observer.observe(el);
    });
});

`;
  }
}
