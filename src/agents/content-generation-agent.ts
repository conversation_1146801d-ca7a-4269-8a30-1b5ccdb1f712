import { Chat<PERSON><PERSON>AI } from "@langchain/openai";
import { ChatAnthropic } from "@langchain/anthropic";
import { createReactAgent } from "@langchain/langgraph/prebuilt";
import { AgentConfig, AgentWorkflowStateType, WebsiteRequirements } from "./types";
import { 
  generateContentTool, 
  createFileTool, 
  handoffToAgentTool,
  updateProgressTool,
  requestApprovalTool 
} from "./tools";

export class ContentGenerationAgent {
  private agent: any;
  private config: AgentConfig;

  constructor(config: Partial<AgentConfig> = {}) {
    this.config = {
      name: "Content Generation Agent",
      type: "content_generation",
      description: "Generates website content including headlines, copy, and text",
      capabilities: [
        "Generate compelling headlines and taglines",
        "Create website copy and descriptions", 
        "Write call-to-action text",
        "Generate navigation labels",
        "Create content for different sections",
        "Adapt tone and style to target audience"
      ],
      tools: ["generate_content", "create_file", "handoff_to_design", "update_progress"],
      model: "gpt-4o",
      provider: "openai",
      systemPrompt: this.getSystemPrompt(),
      ...config,
    };

    this.initializeAgent();
  }

  private getSystemPrompt(): string {
    return `You are a Content Generation Agent specialized in creating compelling website content.

Your responsibilities:
1. Analyze website requirements and target audience
2. Generate appropriate headlines, taglines, and copy
3. Create content that matches the specified tone and style
4. Ensure content is engaging and conversion-focused
5. Adapt content length and complexity to the target audience

Guidelines:
- Always consider the website type and target audience
- Match the specified tone (formal, casual, friendly, professional, creative)
- Create content that supports the website's goals
- Use clear, concise language that converts
- Include relevant keywords naturally
- Structure content for web readability

Available tools:
- generate_content: Generate specific types of content
- create_file: Save generated content to files
- handoff_to_design: Pass control to the design agent
- update_progress: Update workflow progress
- request_approval: Request user approval for content

When you complete content generation, hand off to the design agent to work on visual aspects.`;
  }

  private initializeAgent() {
    const model = this.config.provider === "anthropic" 
      ? new ChatAnthropic({ model: this.config.model || "claude-3-sonnet-20240229" })
      : new ChatOpenAI({ model: this.config.model || "gpt-4o" });

    const tools = [
      generateContentTool,
      createFileTool,
      handoffToAgentTool("design"),
      updateProgressTool,
      requestApprovalTool,
    ];

    this.agent = createReactAgent({
      llm: model,
      tools,
      stateModifier: this.config.systemPrompt,
    });
  }

  async execute(state: AgentWorkflowStateType): Promise<any> {
    const requirements = state.requirements;
    
    // Update progress
    await updateProgressTool.invoke({
      step: "Generating website content",
      completed: 1,
      total: 5,
    });

    // Prepare content generation prompt
    const contentPrompt = this.buildContentPrompt(requirements);
    
    // Execute the agent
    const result = await this.agent.invoke({
      messages: [
        ...state.messages,
        {
          role: "user",
          content: contentPrompt,
        }
      ],
    });

    return result;
  }

  private buildContentPrompt(requirements: WebsiteRequirements): string {
    const { description, type, style, content, targetAudience, features } = requirements;

    return `Generate content for a ${type} website with the following requirements:

Description: ${description}
Style: ${style}
Target Audience: ${targetAudience || "General audience"}
Features: ${features.join(", ")}

Content Requirements:
${content ? `
- Title: ${content.title || "Generate an appropriate title"}
- Sections: ${content.sections?.join(", ") || "Generate appropriate sections"}
- Tone: ${content.copyTone || "professional"}
` : "Generate appropriate content structure and copy"}

Please generate:
1. Main headline/title
2. Tagline or subtitle
3. Navigation menu items
4. Main content sections with copy
5. Call-to-action text
6. Footer content

Create content files for each major section and ensure all content aligns with the website type and target audience.`;
  }

  getConfig(): AgentConfig {
    return this.config;
  }

  async generateHeadline(requirements: WebsiteRequirements): Promise<string> {
    const prompt = `Generate a compelling headline for a ${requirements.type} website: ${requirements.description}`;
    
    return await generateContentTool.invoke({
      type: "headline",
      requirements: prompt,
      tone: requirements.content?.copyTone || "professional",
      length: "short",
    });
  }

  async generateSectionContent(
    sectionName: string, 
    requirements: WebsiteRequirements
  ): Promise<string> {
    const prompt = `Generate content for the "${sectionName}" section of a ${requirements.type} website: ${requirements.description}`;
    
    return await generateContentTool.invoke({
      type: "paragraph",
      requirements: prompt,
      tone: requirements.content?.copyTone || "professional",
      length: "medium",
    });
  }

  async generateNavigation(requirements: WebsiteRequirements): Promise<string[]> {
    const navigationItems = {
      landing_page: ["Home", "About", "Services", "Contact"],
      portfolio: ["Home", "Portfolio", "About", "Services", "Contact"],
      blog: ["Home", "Blog", "About", "Categories", "Contact"],
      ecommerce: ["Home", "Shop", "Categories", "Cart", "Account"],
      corporate: ["Home", "About", "Services", "Team", "Contact"],
      custom: ["Home", "About", "Services", "Contact"],
    };

    return navigationItems[requirements.type] || navigationItems.custom;
  }

  async generateCallToAction(requirements: WebsiteRequirements): Promise<string> {
    const ctaMap = {
      landing_page: "Get Started Today",
      portfolio: "View My Work",
      blog: "Read More",
      ecommerce: "Shop Now",
      corporate: "Contact Us",
      custom: "Learn More",
    };

    return ctaMap[requirements.type] || "Get Started";
  }
}
