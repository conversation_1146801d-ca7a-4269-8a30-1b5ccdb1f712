import { Chat<PERSON><PERSON>A<PERSON> } from "@langchain/openai";
import { ChatAnthropic } from "@langchain/anthropic";
import { createReactAgent } from "@langchain/langgraph/prebuilt";
import { AgentConfig, AgentWorkflowStateType, QualityCriteria } from "./types";
import { 
  readFileTool,
  validateHTMLTool,
  updateProgressTool,
  requestApprovalTool,
  createFileTool 
} from "./tools";

export class QualityAssuranceAgent {
  private agent: any;
  private config: AgentConfig;

  constructor(config: Partial<AgentConfig> = {}) {
    this.config = {
      name: "Quality Assurance Agent",
      type: "quality_assurance",
      description: "Reviews and validates generated websites for quality, accessibility, and best practices",
      capabilities: [
        "Validate HTML structure and syntax",
        "Check CSS for best practices",
        "Review JavaScript for errors",
        "Test accessibility compliance",
        "Verify responsive design",
        "Check SEO optimization",
        "Validate performance metrics"
      ],
      tools: ["read_file", "validate_html", "create_file", "update_progress", "request_approval"],
      model: "gpt-4o",
      provider: "openai",
      systemPrompt: this.getSystemPrompt(),
      ...config,
    };

    this.initializeAgent();
  }

  private getSystemPrompt(): string {
    return `You are a Quality Assurance Agent specialized in reviewing and validating websites for quality and best practices.

Your responsibilities:
1. Validate HTML structure, syntax, and semantic correctness
2. Review CSS for best practices, performance, and maintainability
3. Check JavaScript for errors, security issues, and optimization
4. Test accessibility compliance (WCAG 2.1 guidelines)
5. Verify responsive design across different screen sizes
6. Check SEO optimization and meta tags
7. Validate performance and loading speed considerations

Quality Standards:
- HTML: Valid, semantic, accessible markup
- CSS: Efficient, maintainable, responsive styles
- JavaScript: Error-free, secure, optimized code
- Accessibility: WCAG 2.1 AA compliance
- Performance: Optimized loading and rendering
- SEO: Proper meta tags, structured data, semantic markup
- Cross-browser compatibility
- Mobile responsiveness

Available tools:
- read_file: Read and analyze project files
- validate_html: Validate HTML structure and syntax
- create_file: Create QA reports and documentation
- update_progress: Update workflow progress
- request_approval: Request user approval for final website

Generate comprehensive QA reports with specific recommendations for improvements.`;
  }

  private initializeAgent() {
    const model = this.config.provider === "anthropic" 
      ? new ChatAnthropic({ model: this.config.model || "claude-3-sonnet-20240229" })
      : new ChatOpenAI({ model: this.config.model || "gpt-4o" });

    const tools = [
      readFileTool,
      validateHTMLTool,
      createFileTool,
      updateProgressTool,
      requestApprovalTool,
    ];

    this.agent = createReactAgent({
      llm: model,
      tools,
      stateModifier: this.config.systemPrompt,
    });
  }

  async execute(state: AgentWorkflowStateType): Promise<any> {
    const requirements = state.requirements;
    
    // Update progress
    await updateProgressTool.invoke({
      step: "Performing quality assurance and final validation",
      completed: 5,
      total: 5,
    });

    // Prepare QA prompt
    const qaPrompt = this.buildQAPrompt(requirements, state);
    
    // Execute the agent
    const result = await this.agent.invoke({
      messages: [
        ...state.messages,
        {
          role: "user",
          content: qaPrompt,
        }
      ],
    });

    return result;
  }

  private buildQAPrompt(requirements: any, state: AgentWorkflowStateType): string {
    const files = state.files;
    const fileList = files.map(f => `${f.path} (${f.type})`).join("\n");

    return `Perform comprehensive quality assurance on the generated website project.

Project Type: ${requirements.type}
Requirements: ${requirements.description}

Files to review:
${fileList}

Please conduct a thorough QA review including:

1. HTML Validation:
   - Validate syntax and structure
   - Check semantic markup
   - Verify accessibility attributes
   - Test meta tags and SEO elements

2. CSS Review:
   - Check for best practices
   - Verify responsive design
   - Test cross-browser compatibility
   - Review performance implications

3. JavaScript Analysis:
   - Check for syntax errors
   - Review security considerations
   - Verify functionality
   - Test performance impact

4. Accessibility Testing:
   - WCAG 2.1 compliance
   - Keyboard navigation
   - Screen reader compatibility
   - Color contrast ratios

5. Performance Review:
   - Loading speed optimization
   - Asset optimization
   - Render blocking resources
   - Mobile performance

6. SEO Validation:
   - Meta tags completeness
   - Structured data
   - URL structure
   - Content optimization

Generate a comprehensive QA report with:
- Overall quality score
- Specific issues found
- Recommendations for improvements
- Compliance status for each criteria
- Final approval recommendation

Create a detailed QA report file with all findings and recommendations.`;
  }

  getConfig(): AgentConfig {
    return this.config;
  }

  async performQualityAssurance(files: any[]): Promise<{
    overallScore: number;
    criteria: QualityCriteria;
    issues: string[];
    recommendations: string[];
    approved: boolean;
  }> {
    const results = {
      overallScore: 0,
      criteria: {
        codeQuality: false,
        designConsistency: false,
        contentRelevance: false,
        responsiveness: false,
        accessibility: false,
        performance: false,
        seoOptimization: false,
      },
      issues: [] as string[],
      recommendations: [] as string[],
      approved: false,
    };

    // Validate HTML files
    const htmlFiles = files.filter(f => f.type === "html");
    for (const file of htmlFiles) {
      const validation = await this.validateHTML(file.content);
      if (!validation.isValid) {
        results.issues.push(...validation.issues);
        results.criteria.codeQuality = false;
      } else {
        results.criteria.codeQuality = true;
      }
    }

    // Check CSS files
    const cssFiles = files.filter(f => f.type === "css");
    const cssResults = this.validateCSS(cssFiles);
    results.criteria.designConsistency = cssResults.isValid;
    results.issues.push(...cssResults.issues);
    results.recommendations.push(...cssResults.recommendations);

    // Check JavaScript files
    const jsFiles = files.filter(f => f.type === "js");
    const jsResults = this.validateJavaScript(jsFiles);
    results.issues.push(...jsResults.issues);
    results.recommendations.push(...jsResults.recommendations);

    // Check accessibility
    const accessibilityResults = this.checkAccessibility(htmlFiles);
    results.criteria.accessibility = accessibilityResults.isCompliant;
    results.issues.push(...accessibilityResults.issues);

    // Check responsiveness
    const responsivenessResults = this.checkResponsiveness(cssFiles);
    results.criteria.responsiveness = responsivenessResults.isResponsive;
    results.issues.push(...responsivenessResults.issues);

    // Check SEO
    const seoResults = this.checkSEO(htmlFiles);
    results.criteria.seoOptimization = seoResults.isOptimized;
    results.issues.push(...seoResults.issues);

    // Check performance
    const performanceResults = this.checkPerformance(files);
    results.criteria.performance = performanceResults.isOptimized;
    results.recommendations.push(...performanceResults.recommendations);

    // Calculate overall score
    const criteriaCount = Object.keys(results.criteria).length;
    const passedCriteria = Object.values(results.criteria).filter(Boolean).length;
    results.overallScore = Math.round((passedCriteria / criteriaCount) * 100);

    // Determine approval
    results.approved = results.overallScore >= 80 && results.issues.length === 0;

    return results;
  }

  private async validateHTML(html: string): Promise<{ isValid: boolean; issues: string[] }> {
    return await validateHTMLTool.invoke({ html });
  }

  private validateCSS(cssFiles: any[]): { isValid: boolean; issues: string[]; recommendations: string[] } {
    const issues: string[] = [];
    const recommendations: string[] = [];

    cssFiles.forEach(file => {
      const content = file.content;
      
      // Check for basic CSS best practices
      if (!content.includes("@media")) {
        issues.push("No responsive media queries found");
      }
      
      if (!content.includes("box-sizing")) {
        recommendations.push("Consider adding box-sizing: border-box for better layout control");
      }
      
      if (content.includes("!important")) {
        recommendations.push("Avoid using !important in CSS when possible");
      }
    });

    return {
      isValid: issues.length === 0,
      issues,
      recommendations,
    };
  }

  private validateJavaScript(jsFiles: any[]): { issues: string[]; recommendations: string[] } {
    const issues: string[] = [];
    const recommendations: string[] = [];

    jsFiles.forEach(file => {
      const content = file.content;
      
      // Basic JavaScript validation
      if (content.includes("var ")) {
        recommendations.push("Consider using 'let' or 'const' instead of 'var'");
      }
      
      if (!content.includes("'use strict'") && !content.includes('"use strict"')) {
        recommendations.push("Consider adding 'use strict' for better error handling");
      }
    });

    return { issues, recommendations };
  }

  private checkAccessibility(htmlFiles: any[]): { isCompliant: boolean; issues: string[] } {
    const issues: string[] = [];

    htmlFiles.forEach(file => {
      const content = file.content;
      
      // Check for alt attributes on images
      if (content.includes("<img") && !content.includes("alt=")) {
        issues.push("Images missing alt attributes");
      }
      
      // Check for proper heading hierarchy
      if (!content.includes("<h1")) {
        issues.push("Missing main heading (h1)");
      }
      
      // Check for form labels
      if (content.includes("<input") && !content.includes("label")) {
        issues.push("Form inputs missing labels");
      }
      
      // Check for lang attribute
      if (!content.includes('lang="')) {
        issues.push("Missing lang attribute on html element");
      }
    });

    return {
      isCompliant: issues.length === 0,
      issues,
    };
  }

  private checkResponsiveness(cssFiles: any[]): { isResponsive: boolean; issues: string[] } {
    const issues: string[] = [];

    cssFiles.forEach(file => {
      const content = file.content;
      
      if (!content.includes("@media")) {
        issues.push("No responsive media queries found");
      }
      
      if (!content.includes("viewport")) {
        issues.push("Missing viewport meta tag consideration");
      }
    });

    return {
      isResponsive: issues.length === 0,
      issues,
    };
  }

  private checkSEO(htmlFiles: any[]): { isOptimized: boolean; issues: string[] } {
    const issues: string[] = [];

    htmlFiles.forEach(file => {
      const content = file.content;
      
      if (!content.includes('<meta name="description"')) {
        issues.push("Missing meta description");
      }
      
      if (!content.includes("<title>")) {
        issues.push("Missing title tag");
      }
      
      if (!content.includes('<meta charset="')) {
        issues.push("Missing charset declaration");
      }
      
      if (!content.includes('<meta name="viewport"')) {
        issues.push("Missing viewport meta tag");
      }
    });

    return {
      isOptimized: issues.length === 0,
      issues,
    };
  }

  private checkPerformance(files: any[]): { isOptimized: boolean; recommendations: string[] } {
    const recommendations: string[] = [];

    // Check for potential performance issues
    const cssFiles = files.filter(f => f.type === "css");
    const jsFiles = files.filter(f => f.type === "js");

    if (cssFiles.length > 3) {
      recommendations.push("Consider combining CSS files to reduce HTTP requests");
    }

    if (jsFiles.length > 3) {
      recommendations.push("Consider combining JavaScript files to reduce HTTP requests");
    }

    // Check file sizes (basic check)
    files.forEach(file => {
      if (file.content.length > 100000) { // 100KB
        recommendations.push(`Large file detected: ${file.path} - consider optimization`);
      }
    });

    return {
      isOptimized: recommendations.length === 0,
      recommendations,
    };
  }

  async generateQAReport(qaResults: any): Promise<string> {
    const report = `# Quality Assurance Report

## Overall Score: ${qaResults.overallScore}/100

## Quality Criteria Status

- ✅ Code Quality: ${qaResults.criteria.codeQuality ? "PASS" : "FAIL"}
- ✅ Design Consistency: ${qaResults.criteria.designConsistency ? "PASS" : "FAIL"}
- ✅ Content Relevance: ${qaResults.criteria.contentRelevance ? "PASS" : "FAIL"}
- ✅ Responsiveness: ${qaResults.criteria.responsiveness ? "PASS" : "FAIL"}
- ✅ Accessibility: ${qaResults.criteria.accessibility ? "PASS" : "FAIL"}
- ✅ Performance: ${qaResults.criteria.performance ? "PASS" : "FAIL"}
- ✅ SEO Optimization: ${qaResults.criteria.seoOptimization ? "PASS" : "FAIL"}

## Issues Found

${qaResults.issues.length > 0 ? qaResults.issues.map((issue: string) => `- ❌ ${issue}`).join("\n") : "No critical issues found."}

## Recommendations

${qaResults.recommendations.length > 0 ? qaResults.recommendations.map((rec: string) => `- 💡 ${rec}`).join("\n") : "No recommendations at this time."}

## Final Approval

**Status:** ${qaResults.approved ? "✅ APPROVED" : "❌ REQUIRES FIXES"}

${qaResults.approved 
  ? "The website meets all quality standards and is ready for deployment."
  : "The website requires fixes before it can be approved for deployment."
}

---
*Generated by DeepSite Quality Assurance Agent*
*Report Date: ${new Date().toISOString()}*
`;

    return report;
  }
}
