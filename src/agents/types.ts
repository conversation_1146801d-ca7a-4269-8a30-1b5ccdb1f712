import { BaseMessage } from "@langchain/core/messages";
import { Annotation } from "@langchain/langgraph";

// Core agent types
export type AgentType = 
  | "content_generation"
  | "design"
  | "code_generation"
  | "file_management"
  | "quality_assurance"
  | "supervisor";

// Website requirements interface
export interface WebsiteRequirements {
  description: string;
  type: "landing_page" | "portfolio" | "blog" | "ecommerce" | "corporate" | "custom";
  style: "modern" | "classic" | "minimal" | "creative" | "professional";
  colorScheme?: string;
  features: string[];
  targetAudience?: string;
  content?: {
    title?: string;
    sections?: string[];
    copyTone?: "formal" | "casual" | "friendly" | "professional" | "creative";
  };
}

// Agent task interface
export interface AgentTask {
  id: string;
  type: AgentType;
  description: string;
  requirements: WebsiteRequirements;
  dependencies?: string[]; // IDs of tasks that must complete first
  status: "pending" | "in_progress" | "completed" | "failed" | "requires_approval";
  result?: any;
  feedback?: string;
  createdAt: Date;
  updatedAt: Date;
}

// File structure for website projects
export interface ProjectFile {
  path: string;
  content: string;
  type: "html" | "css" | "js" | "json" | "md" | "txt";
  description?: string;
}

// Agent workflow state
export const AgentWorkflowState = Annotation.Root({
  // Core workflow data
  requirements: Annotation<WebsiteRequirements>({
    reducer: (x, y) => y ?? x,
  }),
  
  // Task management
  tasks: Annotation<AgentTask[]>({
    reducer: (x, y) => [...x, ...y],
    default: () => [],
  }),
  
  // Current active task
  currentTask: Annotation<string | null>({
    reducer: (x, y) => y ?? x,
    default: () => null,
  }),
  
  // Generated files
  files: Annotation<ProjectFile[]>({
    reducer: (x, y) => [...x, ...y],
    default: () => [],
  }),
  
  // Agent communication messages
  messages: Annotation<BaseMessage[]>({
    reducer: (x, y) => x.concat(y),
    default: () => [],
  }),
  
  // Current active agent
  activeAgent: Annotation<AgentType | null>({
    reducer: (x, y) => y ?? x,
    default: () => null,
  }),
  
  // Workflow status
  status: Annotation<"planning" | "executing" | "reviewing" | "completed" | "failed" | "paused">({
    reducer: (x, y) => y ?? x,
    default: () => "planning",
  }),
  
  // User approval required
  requiresApproval: Annotation<boolean>({
    reducer: (x, y) => y ?? x,
    default: () => false,
  }),
  
  // Progress tracking
  progress: Annotation<{
    completed: number;
    total: number;
    currentStep: string;
  }>({
    reducer: (x, y) => y ?? x,
    default: () => ({ completed: 0, total: 0, currentStep: "Initializing" }),
  }),
  
  // Error handling
  errors: Annotation<string[]>({
    reducer: (x, y) => [...x, ...y],
    default: () => [],
  }),
});

// Agent configuration interface
export interface AgentConfig {
  name: string;
  type: AgentType;
  description: string;
  capabilities: string[];
  tools: string[];
  model?: string;
  provider?: string;
  systemPrompt: string;
}

// Tool definitions for agents
export interface AgentTool {
  name: string;
  description: string;
  parameters: Record<string, any>;
  execute: (params: any, context: any) => Promise<any>;
}

// Agent execution result
export interface AgentResult {
  success: boolean;
  data?: any;
  files?: ProjectFile[];
  nextAgent?: AgentType;
  requiresApproval?: boolean;
  message?: string;
  errors?: string[];
}

// Workflow execution context
export interface WorkflowContext {
  sessionId: string;
  userId?: string;
  requirements: WebsiteRequirements;
  currentStep: number;
  totalSteps: number;
  startTime: Date;
  apiKeys?: {
    openai?: string;
    anthropic?: string;
    google?: string;
    openrouter?: string;
  };
}

// Agent handoff interface
export interface AgentHandoff {
  fromAgent: AgentType;
  toAgent: AgentType;
  reason: string;
  data?: any;
  timestamp: Date;
}

// Quality assurance criteria
export interface QualityCriteria {
  codeQuality: boolean;
  designConsistency: boolean;
  contentRelevance: boolean;
  responsiveness: boolean;
  accessibility: boolean;
  performance: boolean;
  seoOptimization: boolean;
}

// Agent performance metrics
export interface AgentMetrics {
  agentType: AgentType;
  tasksCompleted: number;
  averageExecutionTime: number;
  successRate: number;
  lastActive: Date;
}

export type AgentWorkflowStateType = typeof AgentWorkflowState.State;
